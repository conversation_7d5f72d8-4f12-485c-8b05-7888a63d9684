package cn.iocoder.yudao.module.qualification.convert;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.qualification.controller.admin.summary.vo.QualificationSummaryCreateReqVO;
import cn.iocoder.yudao.module.qualification.controller.admin.summary.vo.QualificationSummaryExcelVO;
import cn.iocoder.yudao.module.qualification.controller.admin.summary.vo.QualificationSummaryRespVO;
import cn.iocoder.yudao.module.qualification.controller.admin.summary.vo.QualificationSummaryUpdateReqVO;
import cn.iocoder.yudao.module.qualification.dal.dataobject.QualificationSummaryDO;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T15:07:46+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class QualificationSummaryConvertImpl implements QualificationSummaryConvert {

    @Override
    public QualificationSummaryDO convert(QualificationSummaryCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        QualificationSummaryDO.QualificationSummaryDOBuilder qualificationSummaryDO = QualificationSummaryDO.builder();

        qualificationSummaryDO.brand( bean.getBrand() );
        qualificationSummaryDO.brandCode( bean.getBrandCode() );
        qualificationSummaryDO.expiredStatus( bean.getExpiredStatus() );
        qualificationSummaryDO.firstCategory( bean.getFirstCategory() );
        qualificationSummaryDO.firstCategoryCode( bean.getFirstCategoryCode() );
        qualificationSummaryDO.firstDepartment( bean.getFirstDepartment() );
        qualificationSummaryDO.firstDepartmentCode( bean.getFirstDepartmentCode() );
        qualificationSummaryDO.productLineLevel( bean.getProductLineLevel() );
        qualificationSummaryDO.productLineType( bean.getProductLineType() );
        qualificationSummaryDO.purchaser( bean.getPurchaser() );
        qualificationSummaryDO.purchaserName( bean.getPurchaserName() );
        qualificationSummaryDO.qualificationExpireDate( bean.getQualificationExpireDate() );
        qualificationSummaryDO.remark( bean.getRemark() );
        qualificationSummaryDO.secondCategory( bean.getSecondCategory() );
        qualificationSummaryDO.secondCategoryCode( bean.getSecondCategoryCode() );
        qualificationSummaryDO.status( bean.getStatus() );
        qualificationSummaryDO.thirdCategory( bean.getThirdCategory() );
        qualificationSummaryDO.thirdCategoryCode( bean.getThirdCategoryCode() );
        qualificationSummaryDO.vcAccount( bean.getVcAccount() );

        return qualificationSummaryDO.build();
    }

    @Override
    public QualificationSummaryDO convert(QualificationSummaryUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        QualificationSummaryDO.QualificationSummaryDOBuilder qualificationSummaryDO = QualificationSummaryDO.builder();

        qualificationSummaryDO.brand( bean.getBrand() );
        qualificationSummaryDO.brandCode( bean.getBrandCode() );
        qualificationSummaryDO.expiredStatus( bean.getExpiredStatus() );
        qualificationSummaryDO.firstCategory( bean.getFirstCategory() );
        qualificationSummaryDO.firstCategoryCode( bean.getFirstCategoryCode() );
        qualificationSummaryDO.firstDepartment( bean.getFirstDepartment() );
        qualificationSummaryDO.firstDepartmentCode( bean.getFirstDepartmentCode() );
        qualificationSummaryDO.id( bean.getId() );
        qualificationSummaryDO.productLineLevel( bean.getProductLineLevel() );
        qualificationSummaryDO.productLineType( bean.getProductLineType() );
        qualificationSummaryDO.purchaser( bean.getPurchaser() );
        qualificationSummaryDO.purchaserName( bean.getPurchaserName() );
        qualificationSummaryDO.qualificationExpireDate( bean.getQualificationExpireDate() );
        qualificationSummaryDO.remark( bean.getRemark() );
        qualificationSummaryDO.secondCategory( bean.getSecondCategory() );
        qualificationSummaryDO.secondCategoryCode( bean.getSecondCategoryCode() );
        qualificationSummaryDO.status( bean.getStatus() );
        qualificationSummaryDO.thirdCategory( bean.getThirdCategory() );
        qualificationSummaryDO.thirdCategoryCode( bean.getThirdCategoryCode() );
        qualificationSummaryDO.vcAccount( bean.getVcAccount() );

        return qualificationSummaryDO.build();
    }

    @Override
    public QualificationSummaryRespVO convert(QualificationSummaryDO bean) {
        if ( bean == null ) {
            return null;
        }

        QualificationSummaryRespVO qualificationSummaryRespVO = new QualificationSummaryRespVO();

        qualificationSummaryRespVO.setBrand( bean.getBrand() );
        qualificationSummaryRespVO.setBrandCode( bean.getBrandCode() );
        qualificationSummaryRespVO.setExpiredStatus( bean.getExpiredStatus() );
        qualificationSummaryRespVO.setFirstCategory( bean.getFirstCategory() );
        qualificationSummaryRespVO.setFirstCategoryCode( bean.getFirstCategoryCode() );
        qualificationSummaryRespVO.setFirstDepartment( bean.getFirstDepartment() );
        qualificationSummaryRespVO.setFirstDepartmentCode( bean.getFirstDepartmentCode() );
        qualificationSummaryRespVO.setProductLineLevel( bean.getProductLineLevel() );
        qualificationSummaryRespVO.setProductLineType( bean.getProductLineType() );
        qualificationSummaryRespVO.setPurchaser( bean.getPurchaser() );
        qualificationSummaryRespVO.setPurchaserName( bean.getPurchaserName() );
        qualificationSummaryRespVO.setQualificationExpireDate( bean.getQualificationExpireDate() );
        qualificationSummaryRespVO.setRemark( bean.getRemark() );
        qualificationSummaryRespVO.setSecondCategory( bean.getSecondCategory() );
        qualificationSummaryRespVO.setSecondCategoryCode( bean.getSecondCategoryCode() );
        qualificationSummaryRespVO.setStatus( bean.getStatus() );
        qualificationSummaryRespVO.setThirdCategory( bean.getThirdCategory() );
        qualificationSummaryRespVO.setThirdCategoryCode( bean.getThirdCategoryCode() );
        qualificationSummaryRespVO.setVcAccount( bean.getVcAccount() );
        qualificationSummaryRespVO.setCreateTime( bean.getCreateTime() );
        qualificationSummaryRespVO.setId( bean.getId() );

        return qualificationSummaryRespVO;
    }

    @Override
    public List<QualificationSummaryRespVO> convertList(List<QualificationSummaryDO> list) {
        if ( list == null ) {
            return null;
        }

        List<QualificationSummaryRespVO> list1 = new ArrayList<QualificationSummaryRespVO>( list.size() );
        for ( QualificationSummaryDO qualificationSummaryDO : list ) {
            list1.add( convert( qualificationSummaryDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<QualificationSummaryRespVO> convertPage(PageResult<QualificationSummaryDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<QualificationSummaryRespVO> pageResult = new PageResult<QualificationSummaryRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public QualificationSummaryExcelVO convertToExcel(QualificationSummaryDO bean) {
        if ( bean == null ) {
            return null;
        }

        QualificationSummaryExcelVO qualificationSummaryExcelVO = new QualificationSummaryExcelVO();

        qualificationSummaryExcelVO.setExpiredStatusText( convertExpiredStatus( bean.getExpiredStatus() ) );
        qualificationSummaryExcelVO.setBrand( bean.getBrand() );
        if ( bean.getCreateTime() != null ) {
            qualificationSummaryExcelVO.setCreateTime( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( bean.getCreateTime() ) );
        }
        qualificationSummaryExcelVO.setFirstCategory( bean.getFirstCategory() );
        qualificationSummaryExcelVO.setFirstDepartment( bean.getFirstDepartment() );
        qualificationSummaryExcelVO.setProductLineLevel( bean.getProductLineLevel() );
        qualificationSummaryExcelVO.setProductLineType( bean.getProductLineType() );
        qualificationSummaryExcelVO.setPurchaser( bean.getPurchaser() );
        qualificationSummaryExcelVO.setPurchaserName( bean.getPurchaserName() );
        if ( bean.getQualificationExpireDate() != null ) {
            qualificationSummaryExcelVO.setQualificationExpireDate( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( bean.getQualificationExpireDate() ) );
        }
        qualificationSummaryExcelVO.setRemark( bean.getRemark() );
        qualificationSummaryExcelVO.setSecondCategory( bean.getSecondCategory() );
        qualificationSummaryExcelVO.setStatus( bean.getStatus() );
        qualificationSummaryExcelVO.setThirdCategory( bean.getThirdCategory() );
        qualificationSummaryExcelVO.setVcAccount( bean.getVcAccount() );

        return qualificationSummaryExcelVO;
    }
}
