// ==UserScript==
// @name         京麦数据抓取脚本
// @namespace    jingmai-data-scraper
// @version      1.0.0
// @description  从京麦系统抓取VC账户信息和资质数据，同步到资质管理系统
// <AUTHOR>
// @match        https://shop.jd.com/jdm/home*
// @match        https://seller-v10.shop.jd.com/seller-supplier/quaMenu*
// @require      https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_notification
// @grant        GM_log
// @run-at       document-idle
// ==/UserScript==

// ===== 脚本加载测试 =====
console.log('🚀 [京麦脚本] 开始加载京麦数据抓取脚本');

// 立即暴露一个简单的测试函数，确认脚本是否加载
window.testJingmaiScriptLoaded = function() {
    console.log('✅ [京麦脚本] 脚本已成功加载！');
    console.log('📍 [京麦脚本] 当前页面:', window.location.href);
    console.log('⏰ [京麦脚本] 加载时间:', new Date().toISOString());
    return '京麦脚本已加载';
};

console.log('🎯 [京麦脚本] 可以运行 testJingmaiScriptLoaded() 来测试脚本是否加载');

// ===== 早期全局方法暴露（防止主脚本出错）=====
try {
    window.jingmaiScriptStatus = 'loading';

    // 基础的测试方法
    window.basicJingmaiTest = function() {
        console.log('🧪 [基础测试] 京麦脚本基础功能测试');
        console.log('📍 当前页面:', window.location.href);
        console.log('⏰ 当前时间:', new Date().toISOString());

        // 检查页面类型
        const url = window.location.href;
        let pageType = 'unknown';
        if (url.includes('shop.jd.com/jdm/home')) {
            pageType = 'account';
        } else if (url.includes('seller-v10.shop.jd.com/seller-supplier/quaMenu')) {
            pageType = 'qualification';
        }

        console.log('📄 页面类型:', pageType);

        // 检查常见的h5st对象
        const h5stCheck = ['_JdJrTdRiskFpInfo', 'jdJrRiskFpInfo', 'generateH5st'].map(name => ({
            name,
            exists: !!window[name],
            type: typeof window[name]
        }));

        console.log('🔧 H5ST对象检查:', h5stCheck);

        return {
            pageType,
            h5stObjects: h5stCheck.filter(obj => obj.exists),
            timestamp: Date.now()
        };
    };

    console.log('✅ [京麦脚本] 基础测试方法已暴露: basicJingmaiTest()');

} catch (error) {
    console.error('❌ [京麦脚本] 早期方法暴露失败:', error);
}

(function() {
    'use strict';

    // ===== GM函数兼容性检查 =====
    if (typeof GM_getValue === 'undefined') {
        console.error('GM_getValue 函数不可用，脚本可能无法正常工作');
    }
    if (typeof GM_setValue === 'undefined') {
        console.error('GM_setValue 函数不可用，脚本可能无法正常工作');
    }
    if (typeof GM_notification === 'undefined') {
        // 如果GM_notification不可用，提供一个备用实现
        window.GM_notification = function(title, text, image) {
            console.log(`[${title}] ${text}`);
            if (typeof alert !== 'undefined') {
                alert(`${title}: ${text}`);
            }
        };
    }

    // ===== 配置模块 =====
    
    // 一次性更新API地址（处理从旧地址迁移）
    (() => {
        const newDefaultUrl = 'http://43.137.41.125:8088/admin-api/qualification/sync';
        const oldUrls = [
            'http://127.0.0.1:48080/admin-api/qualification/sync',
            'http://127.0.0.1:28080/admin-api/qualification/sync'
        ];
        const currentUrl = GM_getValue('API_BASE_URL', '');
        
        // 如果当前是旧地址或者为空，更新为新地址
        if (!currentUrl || oldUrls.includes(currentUrl)) {
            GM_setValue('API_BASE_URL', newDefaultUrl);
            console.log(`[京麦数据抓取脚本] API地址已更新为: ${newDefaultUrl}`);
        }
    })();
    
    const CONFIG = {
        // API配置
        API_BASE_URL: GM_getValue('API_BASE_URL', 'http://43.137.41.125:8088/admin-api/qualification/sync'),
        API_TOKEN: GM_getValue('API_TOKEN', ''),
        
        // 抓取配置
        AUTO_SCRAPE: GM_getValue('AUTO_SCRAPE', true), // 默认开启自动同步
        SCRAPE_INTERVAL: GM_getValue('SCRAPE_INTERVAL', 60000), // 60秒检查一次
        MAX_RETRY_COUNT: GM_getValue('MAX_RETRY_COUNT', 3),
        BATCH_SIZE: GM_getValue('BATCH_SIZE', 10),
        FIRST_SETUP_DONE: GM_getValue('FIRST_SETUP_DONE', false), // 是否完成初次设置
        MANAGER_NAME: GM_getValue('MANAGER_NAME', ''), // 管理员姓名
        
        // 界面配置
        PANEL_POSITION: GM_getValue('PANEL_POSITION', 'top-right'),
        SHOW_LOGS: GM_getValue('SHOW_LOGS', true),
        
        // 京麦接口配置
        JINGMAI_APIS: {
            ACCOUNT_INFO: 'https://sff.jd.com/api?v=1.0&appId=1VLIXQT5B1IEYHXSVHRV&api=dsm.shop.pageframe.navigation.accountFacade.findAccountInfo',
            QUALIFICATION_LIST: 'https://sff.jd.com/api?v=1.0&appId=XRICZR1JYFQENYERSMUP&api=dsm.omni.qua.product.BrandRelationReadService.queryBrandRelationFormalList'
        }
    };
    
    // ===== 工具模块 =====
    const Utils = {
        // 日志记录
        log: function(level, message, data = null) {
            const timestamp = new Date().toISOString();
            const logMessage = `[京麦脚本] [${level}] ${message}`;

            // 简化控制台输出，只显示重要信息
            if (level === 'ERROR') {
                console.error(logMessage, data || '');
            } else if (level === 'WARN') {
                console.warn(logMessage);
            } else if (level === 'INFO' && (
                message.includes('同步成功') ||
                message.includes('上传完成') ||
                message.includes('抓取完成') ||
                message.includes('初始化完成')
            )) {
                console.log(logMessage);
            }
            // 其他INFO级别日志不在控制台显示，只存储

            GM_log(logMessage + (data ? JSON.stringify(data) : ''));

            // 存储日志到本地
            const logs = GM_getValue('scrape_logs', []);
            logs.push({
                timestamp,
                level,
                message,
                data
            });

            // 只保留最近1000条日志
            if (logs.length > 1000) {
                logs.splice(0, logs.length - 1000);
            }

            GM_setValue('scrape_logs', logs);
        },
        
        // 延迟执行
        delay: function(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },
        
        // 格式化日期为YYYY-MM-DD格式
        formatDate: function(timestamp) {
            if (!timestamp) return '';
            
            let date;
            if (typeof timestamp === 'number') {
                date = new Date(timestamp);
            } else if (typeof timestamp === 'string') {
                // 处理多种日期格式
                if (timestamp.includes('/')) {
                    // 处理 2026/12/23 格式
                    const parts = timestamp.split('/');
                    if (parts.length === 3) {
                        date = new Date(parts[0], parts[1] - 1, parts[2]);
                    }
                } else {
                    date = new Date(timestamp);
                }
            } else {
                date = new Date(timestamp);
            }
            
            if (isNaN(date.getTime())) {
                return '';
            }
            
            // 返回 YYYY-MM-DD 格式
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        
        // 生成UUID
        generateUUID: function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        },
        
        // 检查页面类型
        getPageType: function() {
            const url = window.location.href;
            if (url.includes('shop.jd.com/jdm/home')) {
                return 'account';
            } else if (url.includes('seller-v10.shop.jd.com/seller-supplier/quaMenu')) {
                return 'qualification';
            }
            return 'unknown';
        }
    };
    
    // ===== H5ST生成器模块 =====
    const H5stGenerator = {
        // 查找页面中的h5st生成函数
        findH5stFunction: function() {
            console.log('🔍 [H5ST] 开始查找页面中的h5st生成函数');

            // 常见的h5st相关全局对象名称
            const possibleNames = [
                '_JdJrTdRiskFpInfo',
                'jdJrRiskFpInfo',
                'generateH5st',
                'getH5st',
                'h5st',
                'JdJrTdRiskFpInfo',
                'risk',
                'fp',
                'fingerprint',
                'jdRisk'
            ];

            // 遍历可能的函数名
            for (let name of possibleNames) {
                if (window[name] && typeof window[name] === 'object') {
                    console.log(`🎯 [H5ST] 找到可能的h5st对象: ${name}`, window[name]);

                    // 检查是否有getData方法
                    if (typeof window[name].getData === 'function') {
                        console.log(`✅ [H5ST] 找到h5st生成函数: ${name}.getData`);
                        return { obj: window[name], method: 'getData', name: name };
                    }

                    // 检查是否有getH5st方法
                    if (typeof window[name].getH5st === 'function') {
                        console.log(`✅ [H5ST] 找到h5st生成函数: ${name}.getH5st`);
                        return { obj: window[name], method: 'getH5st', name: name };
                    }
                }
            }

            // 深度搜索全局对象
            return this.deepSearchH5stFunction();
        },

        // 深度搜索h5st函数
        deepSearchH5stFunction: function() {
            console.log('🔍 [H5ST] 开始深度搜索h5st函数');

            const searchedObjects = new Set();

            const searchObject = (obj, path = 'window') => {
                if (!obj || typeof obj !== 'object' || searchedObjects.has(obj)) {
                    return null;
                }

                searchedObjects.add(obj);

                // 检查当前对象的方法
                for (let key in obj) {
                    try {
                        const value = obj[key];

                        // 检查是否是h5st相关方法
                        if (typeof value === 'function') {
                            const funcStr = value.toString();
                            if (funcStr.includes('h5st') ||
                                funcStr.includes('H5ST') ||
                                (funcStr.includes('timestamp') && funcStr.includes('signature'))) {
                                console.log(`🎯 [H5ST] 找到可能的h5st函数: ${path}.${key}`);
                                return { obj, method: key, path: `${path}.${key}` };
                            }
                        }

                        // 递归搜索子对象（限制深度）
                        if (typeof value === 'object' && value !== null && path.split('.').length < 4) {
                            const result = searchObject(value, `${path}.${key}`);
                            if (result) return result;
                        }
                    } catch (e) {
                        // 忽略访问错误
                    }
                }

                return null;
            };

            return searchObject(window);
        },

        // 获取h5st参数
        getH5st: async function(requestParams) {
            console.log('🔧 [H5ST] 开始生成h5st参数');

            // 首先尝试查找现有函数
            const h5stObj = this.findH5stFunction();

            if (h5stObj) {
                try {
                    // 准备参数，增加更多的参数以提高成功率
                    const params = this.prepareH5stParams(requestParams);
                    console.log('📤 [H5ST] 准备的参数:', params);

                    // 调用生成函数，增加重试机制
                    let result = null;
                    const maxRetries = 3;
                    
                    for (let i = 0; i < maxRetries; i++) {
                        try {
                            result = h5stObj.obj[h5stObj.method](params);

                            // 如果返回Promise，等待结果
                            if (result && typeof result.then === 'function') {
                                result = await result;
                            }

                            // 验证结果的有效性
                            if (this.validateH5st(result)) {
                                console.log('✅ [H5ST] h5st生成成功，验证通过');
                                break;
                            } else {
                                console.warn(`⚠️ [H5ST] 第${i + 1}次生成的h5st验证失败，重试...`);
                                result = null;
                            }
                        } catch (error) {
                            console.warn(`⚠️ [H5ST] 第${i + 1}次生成失败:`, error);
                            if (i < maxRetries - 1) {
                                await this.delay(1000); // 重试前等待1秒
                            }
                        }
                    }

                    console.log('📥 [H5ST] 最终生成结果:', result);

                    // 提取h5st字符串
                    if (typeof result === 'string') {
                        return result;
                    } else if (result && result.h5st) {
                        return result.h5st;
                    } else if (result && result.data) {
                        return result.data;
                    }

                    return result;

                } catch (error) {
                    console.error('❌ [H5ST] 调用h5st函数失败:', error);
                }
            }

            // 如果找不到函数，尝试其他方法
            return await this.alternativeH5stGeneration(requestParams);
        },

        // 验证H5ST的有效性
        validateH5st: function(h5st) {
            if (!h5st) return false;
            
            const h5stStr = typeof h5st === 'string' ? h5st : 
                           (h5st.h5st || h5st.data || '');
            
            // 基本格式验证
            if (typeof h5stStr !== 'string' || h5stStr.length < 10) {
                return false;
            }
            
            // 检查是否包含必要的组件（时间戳、签名等）
            if (!h5stStr.includes('&') || !h5stStr.includes('=')) {
                return false;
            }
            
            console.log('📊 [H5ST验证] h5st格式验证通过，长度:', h5stStr.length);
            return true;
        },

        // 延迟函数
        delay: function(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },

        // 准备h5st生成所需的参数
        prepareH5stParams: function(requestParams) {
            const baseParams = {
                appId: 'XRICZR1JYFQENYERSMUP',
                api: 'dsm.omni.qua.product.BrandRelationReadService.queryBrandRelationFormalList',
                version: '1.0',
                timestamp: Date.now(),
                url: window.location.href,
                method: 'POST'
            };

            // 如果有请求参数，添加到data中
            if (requestParams) {
                baseParams.data = requestParams;
                baseParams.body = typeof requestParams === 'string' ? requestParams : JSON.stringify(requestParams);
            }

            return baseParams;
        },

        // 备用h5st生成方法
        alternativeH5stGeneration: async function(requestParams) {
            console.log('🔄 [H5ST] 尝试备用h5st生成方法');

            try {
                // 方法1：通过脚本注入获取
                const h5st = await this.getH5stViaInjection(requestParams);
                if (h5st) return h5st;

                // 方法2：拦截页面原生请求
                return await this.interceptNativeRequest(requestParams);
            } catch (error) {
                console.error('❌ [H5ST] 备用方法失败:', error);
                return null;
            }
        },

        // 通过脚本注入获取h5st
        getH5stViaInjection: function(requestParams) {
            return new Promise((resolve, reject) => {
                console.log('💉 [H5ST] 开始脚本注入方法');

                // 检查是否已经注入过脚本
                if (!window.h5stInjected) {
                    this.injectH5stScript();
                    window.h5stInjected = true;
                }

                const requestId = Date.now().toString();

                // 监听页面返回的消息
                const messageHandler = (event) => {
                    if (event.data && event.data.type === 'H5ST_RESULT' && event.data.requestId === requestId) {
                        window.removeEventListener('message', messageHandler);
                        console.log('✅ [H5ST] 通过注入获取到h5st:', event.data.h5st);
                        resolve(event.data.h5st);
                    }
                };

                window.addEventListener('message', messageHandler);

                // 发送获取h5st的请求
                window.postMessage({
                    type: 'GET_H5ST',
                    params: this.prepareH5stParams(requestParams),
                    requestId: requestId
                }, '*');

                // 超时处理
                setTimeout(() => {
                    window.removeEventListener('message', messageHandler);
                    console.warn('⏰ [H5ST] 脚本注入方法超时');
                    resolve(null);
                }, 5000);
            });
        },

        // 注入h5st获取脚本到页面
        injectH5stScript: function() {
            console.log('💉 [H5ST] 注入h5st获取脚本到页面');

            const script = document.createElement('script');
            script.textContent = `
                // 在页面环境中运行的代码
                (function() {
                    console.log('🔧 [H5ST注入] 脚本已在页面环境中运行');

                    window.getH5stForTampermonkey = function(params) {
                        try {
                            console.log('🔧 [H5ST注入] 开始在页面环境中生成h5st', params);

                            // 查找h5st生成函数
                            const generators = [
                                window._JdJrTdRiskFpInfo,
                                window.jdJrRiskFpInfo,
                                window.generateH5st,
                                window.jdRisk,
                                window.risk
                            ];

                            for (let generator of generators) {
                                if (generator && typeof generator === 'object') {
                                    console.log('🎯 [H5ST注入] 找到生成器对象:', generator);

                                    if (typeof generator.getData === 'function') {
                                        console.log('📞 [H5ST注入] 调用getData方法');
                                        const result = generator.getData(params);
                                        console.log('📥 [H5ST注入] getData结果:', result);
                                        return result;
                                    }

                                    if (typeof generator.getH5st === 'function') {
                                        console.log('📞 [H5ST注入] 调用getH5st方法');
                                        const result = generator.getH5st(params);
                                        console.log('📥 [H5ST注入] getH5st结果:', result);
                                        return result;
                                    }
                                }
                            }

                            console.warn('⚠️ [H5ST注入] 未找到有效的h5st生成函数');
                            return null;
                        } catch (error) {
                            console.error('❌ [H5ST注入] 页面环境h5st生成失败:', error);
                            return null;
                        }
                    };

                    // 监听来自Tampermonkey的消息
                    window.addEventListener('message', function(event) {
                        if (event.data && event.data.type === 'GET_H5ST') {
                            console.log('📨 [H5ST注入] 收到h5st生成请求:', event.data);

                            const h5st = window.getH5stForTampermonkey(event.data.params);

                            window.postMessage({
                                type: 'H5ST_RESULT',
                                h5st: h5st,
                                requestId: event.data.requestId
                            }, '*');
                        }
                    });

                    console.log('✅ [H5ST注入] 消息监听器已设置');
                })();
            `;

            document.head.appendChild(script);
            console.log('✅ [H5ST] h5st获取脚本已注入到页面');
        },

        // 拦截页面原生请求获取h5st
        interceptNativeRequest: function(requestParams) {
            return new Promise((resolve, reject) => {
                console.log('🎣 [H5ST] 开始拦截原生请求获取h5st');

                // 保存原始的XMLHttpRequest和fetch
                const originalXHR = window.XMLHttpRequest;
                const originalFetch = window.fetch;
                const originalOpen = originalXHR.prototype.open;
                const originalSend = originalXHR.prototype.send;

                let intercepted = false;

                // 重写XMLHttpRequest
                const newXHR = function() {
                    const xhr = new originalXHR();

                    xhr.open = function(method, url, ...args) {
                        this._method = method;
                        this._url = url;
                        this._headers = {};
                        return originalOpen.apply(this, [method, url, ...args]);
                    };

                    xhr.setRequestHeader = function(name, value) {
                        this._headers[name] = value;
                        if (name.toLowerCase() === 'h5st' && this._url && this._url.includes('queryBrandRelationFormalList')) {
                            console.log('🎯 [H5ST] 拦截到h5st参数:', value);
                            intercepted = true;
                            resolve(value);

                            // 恢复原始函数
                            window.XMLHttpRequest = originalXHR;
                            window.fetch = originalFetch;
                        }
                        return originalXHR.prototype.setRequestHeader.apply(this, [name, value]);
                    };

                    return xhr;
                };

                // 重写fetch
                window.fetch = function(url, options = {}) {
                    if (typeof url === 'string' && url.includes('queryBrandRelationFormalList')) {
                        const h5st = options.headers?.h5st || options.headers?.H5st;
                        if (h5st) {
                            console.log('🎯 [H5ST] 从fetch请求中拦截到h5st:', h5st);
                            intercepted = true;
                            resolve(h5st);

                            // 恢复原始函数
                            window.XMLHttpRequest = originalXHR;
                            window.fetch = originalFetch;
                        }
                    }
                    return originalFetch.apply(this, arguments);
                };

                window.XMLHttpRequest = newXHR;

                // 触发一个真实的请求
                setTimeout(() => {
                    this.triggerNativeRequest(requestParams);
                }, 1000);

                // 超时处理
                setTimeout(() => {
                    if (!intercepted) {
                        window.XMLHttpRequest = originalXHR;
                        window.fetch = originalFetch;
                        console.warn('⏰ [H5ST] 拦截原生请求超时');
                        resolve(null);
                    }
                }, 10000);
            });
        },

        // 触发页面原生请求
        triggerNativeRequest: function(requestParams) {
            console.log('🚀 [H5ST] 触发页面原生请求');

            try {
                // 方法1：模拟点击页面元素
                const searchBtn = document.querySelector('button[type="submit"], .search-btn, .query-btn, .btn-search');
                if (searchBtn && searchBtn.offsetParent !== null) { // 确保元素可见
                    console.log('🖱️ [H5ST] 模拟点击搜索按钮');
                    searchBtn.click();
                    return;
                }

                // 方法2：查找分页按钮
                const pageBtn = document.querySelector('.ant-pagination-next, .pagination-next, .next-page');
                if (pageBtn && pageBtn.offsetParent !== null) {
                    console.log('🖱️ [H5ST] 模拟点击分页按钮');
                    pageBtn.click();
                    return;
                }

                // 方法3：触发页面事件
                const event = new Event('search', { bubbles: true });
                document.dispatchEvent(event);

                // 方法4：调用页面的查询函数
                if (window.search || window.query || window.loadData) {
                    const func = window.search || window.query || window.loadData;
                    if (typeof func === 'function') {
                        console.log('📞 [H5ST] 调用页面查询函数');
                        func(requestParams);
                    }
                }

            } catch (error) {
                console.error('❌ [H5ST] 触发原生请求失败:', error);
            }
        }
    };

    // ===== 数据抓取模块 =====
    const DataScraper = {
        // 初始化请求拦截
        init: function() {
            // 先初始化属性
            this.cachedRealRequestData = null;
            this.interceptRetryCount = 0;
            this.maxInterceptRetries = 5; // 增加重试次数
            this.lastReinitTime = 0; // 上次重新初始化时间

            // 防检测机制已完全禁用
            // this.setupAntiDetection();

            // 然后启动拦截
            this.interceptPageRequests();

            // DOM观察器已禁用，避免影响页面性能
            // this.setupDOMObserver();

            // 自动重新初始化已禁用，避免干扰页面运行
            // 如需重新初始化，请使用控制台命令 reinitJingmaiIntercept()

            // 定期检查已禁用，避免频繁执行影响页面
            // 如需检查拦截状态，请使用控制台命令 checkJingmaiIntercept()

            Utils.log('INFO', '数据抓取模块已初始化，开始监听页面请求');
        },

        // 设置防检测机制（禁用，避免影响页面正常功能）
        setupAntiDetection: function() {
            console.log('🛡️ [防检测] 防检测机制已禁用，避免影响页面功能');
            
            // 原有的防检测功能可能导致页面异常，暂时禁用
            // 只保留最基本的webdriver隐藏
            this.hideBasicAutomationSignatures();
        },

        // 基本的自动化特征隐藏（仅隐藏webdriver属性）
        hideBasicAutomationSignatures: function() {
            try {
                // 只隐藏webdriver属性，不修改其他浏览器特征
                if (typeof navigator.webdriver !== 'undefined') {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
                    console.log('🛡️ [防检测] 已隐藏webdriver属性');
                }
            } catch (error) {
                // 如果修改失败，静默处理，不影响页面功能
                console.log('🛡️ [防检测] webdriver属性修改失败，忽略');
            }
        },

        // 重新初始化拦截
        reinitializeIntercept: function() {
            const now = Date.now();

            // 避免过于频繁的重新初始化
            if (this.lastReinitTime && (now - this.lastReinitTime) < 5000) {
                console.log('🔄 [京麦拦截] 重新初始化冷却中，跳过');
                return;
            }

            if (this.interceptRetryCount < this.maxInterceptRetries) {
                this.interceptRetryCount++;
                this.lastReinitTime = now;

                console.log(`🔄 [京麦拦截] 重新初始化拦截机制 (第${this.interceptRetryCount}次)`);
                this.interceptPageRequests();

                // 自动重新初始化已禁用，避免频繁操作
                // if (this.interceptRetryCount <= 2) {
                //     setTimeout(() => {
                //         this.reinitializeIntercept();
                //     }, 10000);
                // }
            } else {
                console.log('🔄 [京麦拦截] 已达到最大重试次数，停止重新初始化');
            }
        },

        // 拦截页面的真实请求（已禁用，使用全局拦截器）
        interceptPageRequests: function() {
            console.log('🔄 [京麦拦截] 拦截器设置已禁用，使用全局拦截器');
            Utils.log('INFO', '拦截器设置已禁用，使用全局拦截器');
            
            // 不再设置拦截器，避免与全局拦截器冲突
            // this.interceptWithSimpleMethod();
        },

        // 简单有效的拦截方法（基于成功的拦截器逻辑）
        interceptWithSimpleMethod: function() {
            const self = this;
            const TARGET_API = CONFIG.JINGMAI_APIS.QUALIFICATION_LIST;

            console.log('🎯 [京麦拦截] 设置简单拦截器，目标API:', TARGET_API);

            // 拦截fetch请求（主要方式）
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const [resource, config = {}] = args;
                
                // 检查是否是目标API（不修改请求，只拦截响应）
                if (resource === TARGET_API || (typeof resource === 'object' && resource.url === TARGET_API)) {
                    console.log('🎯 [京麦拦截] 检测到目标API请求:', resource);
                    
                    // 保存请求信息到缓存
                        self.cachedRealRequestData = {
                        url: resource,
                        method: config.method || 'POST',
                        headers: { ...config.headers },
                        body: config.body,
                            timestamp: Date.now(),
                            cookies: document.cookie,
                            userAgent: navigator.userAgent,
                            referer: window.location.href
                        };

                    // 调用原始fetch并拦截响应
                    return originalFetch.apply(this, args)
                        .then(response => {
                            // 克隆响应以便读取数据而不影响原始请求
                            const responseClone = response.clone();
                            
                            // 读取并处理响应数据
                            responseClone.json()
                                .then(data => {
                                    console.group('🎉 [京麦拦截] 成功拦截到资质API响应');
                                    console.log('📊 响应状态:', response.status, response.statusText);
                                    console.log('📋 完整响应数据:', data);
                                    
                                    if (data.data && data.data.itemList) {
                                        console.log('📝 资质列表数据:');
                                        console.table(data.data.itemList.slice(0, 5)); // 只显示前5条避免刷屏
                                        console.log('📄 分页信息:', data.data.pageInfoFacet);
                                        console.log('🔢 总计:', data.data.pageInfoFacet?.totalCount, '个品牌关系记录');
                                        console.log('📝 本页数据:', data.data.itemList.length, '条');
                                    }
                                    console.groupEnd();

                                    // 缓存响应数据
                                    self.cacheRealResponse(data);
                                    Utils.log('INFO', '已缓存真实请求的响应数据');

                                    // 立即通知UI更新
                                    if (window.UI && typeof window.UI.updateStatistics === 'function') {
                                        setTimeout(() => {
                                            window.UI.updateStatistics();
                                        }, 100);
                                    }
                                })
                                .catch(err => {
                                    console.error('❌ [京麦拦截] 解析响应数据失败:', err);
                                });
                            
                            // 返回原始响应给页面使用
                            return response;
                        })
                        .catch(err => {
                            console.error('❌ [京麦拦截] API请求失败:', err);
                            throw err;
                        });
                }
                
                // 非目标API，直接通过
                return originalFetch.apply(this, args);
            };
            
            // 拦截XMLHttpRequest请求（备用方案）
            const originalXHROpen = XMLHttpRequest.prototype.open;
            const originalXHRSend = XMLHttpRequest.prototype.send;
            
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._method = method;
                this._url = url;
                return originalXHROpen.apply(this, [method, url, ...args]);
            };
            
            XMLHttpRequest.prototype.send = function(data) {
                if (this._url === TARGET_API) {
                    console.log('🎯 [京麦拦截] 检测到目标API请求(XMLHttpRequest):', this._url);
                    
                    // 保存请求信息到缓存
                        self.cachedRealRequestData = {
                        url: this._url,
                        method: this._method,
                        headers: {}, // XHR headers需要特殊处理，这里简化
                        body: data,
                            timestamp: Date.now(),
                            cookies: document.cookie,
                            userAgent: navigator.userAgent,
                            referer: window.location.href
                        };

                    // 监听响应（不修改请求）
                    this.addEventListener('load', function() {
                        if (this.status === 200) {
                            try {
                                const responseData = JSON.parse(this.responseText);
                                console.group('🎉 [京麦拦截] 成功拦截到资质API响应(XMLHttpRequest)');
                                console.log('📊 响应状态:', this.status, this.statusText);
                                console.log('📋 完整响应数据:', responseData);
                                
                                if (responseData.data && responseData.data.itemList) {
                                    console.log('📝 资质列表数据:');
                                    console.table(responseData.data.itemList.slice(0, 5));
                                    console.log('📄 分页信息:', responseData.data.pageInfoFacet);
                                    console.log('🔢 总计:', responseData.data.pageInfoFacet?.totalCount, '个品牌关系记录');
                                }
                            console.groupEnd();

                                // 缓存响应数据
                                self.cacheRealResponse(responseData);
                                Utils.log('INFO', '已缓存真实请求的响应数据(XMLHttpRequest)');
                                
                            } catch (err) {
                                console.error('❌ [京麦拦截] 解析XMLHttpRequest响应数据失败:', err);
                            }
                        }
                    });
                }
                
                return originalXHRSend.apply(this, arguments);
            };

            console.log('✅ [京麦拦截] 简单拦截器设置完成');
        },

        // 使用MutationObserver监听页面变化，确保拦截不会失效
        setupDOMObserver: function() {
            const self = this;
            let lastReinitTime = 0;
            const REINIT_COOLDOWN = 10000; // 10秒冷却时间

            // 监听页面内容变化
            const observer = new MutationObserver((mutations) => {
                const now = Date.now();

                // 检查是否在冷却时间内
                if (now - lastReinitTime < REINIT_COOLDOWN) {
                    return;
                }

                // 只关注重要的DOM变化
                let hasSignificantChange = false;
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否有重要的节点变化（如表格、列表等）
                        for (let node of mutation.addedNodes) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                const tagName = node.tagName?.toLowerCase();
                                if (tagName === 'table' || tagName === 'tbody' ||
                                    node.className?.includes('table') ||
                                    node.className?.includes('list') ||
                                    node.querySelector?.('table, .table, .list')) {
                                    hasSignificantChange = true;
                                    break;
                                }
                            }
                        }
                    }
                });

                if (hasSignificantChange) {
                    lastReinitTime = now;
                    setTimeout(() => {
                        console.log('🔄 [京麦拦截] 检测到重要页面变化，重新初始化拦截');
                        self.interceptPageRequests();
                    }, 2000);
                }
            });

            // 开始观察，但限制观察范围
            if (document.body) {
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: false, // 不监听属性变化
                    characterData: false // 不监听文本变化
                });

                console.log('🔄 [京麦拦截] DOM观察器已启动（优化版）');
            }
        },

        // 缓存真实响应数据
        cacheRealResponse: function(responseData) {
            const cachedData = {
                timestamp: Date.now(),
                response: responseData,
                requestData: this.cachedRealRequestData
            };
            
            GM_setValue('cached_real_response', cachedData);
            Utils.log('INFO', '已更新缓存的真实响应数据');
        },

        // 获取VC账户信息
        getAccountInfo: async function() {
            Utils.log('INFO', '开始获取VC账户信息');

            // 增加随机延迟，模拟手动操作 (2-5秒)
            const delay = Math.random() * 3000 + 2000;
            Utils.log('INFO', `等待${Math.round(delay)}ms后开始请求`);
            await Utils.delay(delay);

            try {
                const response = await this.makeRequest(CONFIG.JINGMAI_APIS.ACCOUNT_INFO, {
                    method: 'POST',
                    headers: this.getCommonHeaders(),
                    body: '{}'
                });
                
                if (response.code === 200 && response.data) {
                    Utils.log('INFO', '成功获取VC账户信息', response.data);
                    return this.transformAccountData(response.data);
                } else {
                    throw new Error(`获取账户信息失败: ${response.msg || '未知错误'}`);
                }
            } catch (error) {
                Utils.log('ERROR', '获取VC账户信息失败', error.message);
                throw error;
            }
        },
        
        // 获取资质列表数据 - 简化版，基于拦截到的数据
        getQualificationList: async function(pageNum = 1, pageSize = 10, retryCount = 0) {
            Utils.log('INFO', `手动抓取模式 - 等待用户操作获取第${pageNum}页数据`);
            
            // 检查是否有拦截到的数据
            const cachedRealData = GM_getValue('cached_real_response', null);
            
            if (cachedRealData && cachedRealData.response) {
                Utils.log('INFO', '发现拦截到的响应数据，直接使用');
                return cachedRealData.response.data;
            } else {
                // 没有拦截数据，提示用户手动操作
                Utils.log('WARN', '未发现拦截数据，请在页面上手动操作（翻页、筛选等）来触发API请求');
                throw new Error('请先在页面上进行操作（翻页、筛选等）以触发数据拦截');
            }
        },

        // 简化版：基于拦截的数据处理

        // 手动检查拦截状态
        checkInterceptStatus: function() {
            console.group('🔍 [京麦拦截] 拦截状态检查');
            console.log('📊 缓存的请求数据:', this.cachedRealRequestData);
            console.log('📊 拦截重试次数:', this.interceptRetryCount);
            console.log('📊 当前页面URL:', window.location.href);
            console.log('📊 页面类型:', Utils.getPageType());

            // 检查页面中是否有相关的网络请求
            const performanceEntries = performance.getEntriesByType('resource');
            const relevantRequests = performanceEntries.filter(entry =>
                entry.name.includes('queryBrandRelationFormalList')
            );
            console.log('📊 相关网络请求:', relevantRequests);
            console.groupEnd();

            return {
                hasCachedData: !!this.cachedRealRequestData,
                retryCount: this.interceptRetryCount,
                pageType: Utils.getPageType(),
                relevantRequests: relevantRequests.length
            };
        },

        // 获取所有资质数据 - 基于拦截的手动抓取模式
        getAllQualifications: async function() {
            Utils.log('INFO', '开始获取资质数据（手动拦截模式）');

            // 检查拦截状态
            const interceptStatus = this.checkInterceptStatus();
            console.log('🔍 [京麦拦截] 当前拦截状态:', interceptStatus);

            // 如果没有缓存数据且在资质页面，重新初始化拦截
            if (!interceptStatus.hasCachedData && interceptStatus.pageType === 'qualification') {
                console.log('⚠️ [京麦拦截] 在资质页面但未找到缓存数据，重新初始化拦截');
                this.reinitializeIntercept();
                await Utils.delay(2000);
            }

            try {
                // 基于拦截数据的简单抓取
                const cachedData = GM_getValue('cached_real_response', null);
                
                if (!cachedData || !cachedData.response) {
                    const errorMsg = '未发现拦截到的数据，请先在页面上进行操作（翻页、筛选等）';
                    Utils.log('WARN', errorMsg);
                    throw new Error(errorMsg);
                }
                
                const responseData = cachedData.response.data;
                if (!responseData || !responseData.itemList) {
                    throw new Error('拦截到的数据格式异常');
                }
                
                const itemList = responseData.itemList;
                const totalCount = responseData.pageInfoFacet?.totalCount || itemList.length;
                
                Utils.log('INFO', `从拦截数据中获取到 ${itemList.length} 条资质记录，总计 ${totalCount} 条`);
                
                // 提示用户如何获取更多数据
                if (itemList.length < totalCount) {
                    const remainingCount = totalCount - itemList.length;
                    Utils.log('INFO', `还有 ${remainingCount} 条数据未获取，请继续在页面上翻页操作`);
                    GM_notification('数据获取提示', `已获取 ${itemList.length}/${totalCount} 条数据，请继续翻页获取更多`, 'info');
                } else {
                    Utils.log('INFO', '已获取当前页面的所有数据');
                    GM_notification('数据获取完成', `已获取 ${itemList.length} 条资质数据`, 'success');
                }
                
                // 转换数据格式
                return itemList.map(item => this.transformQualificationData(item));
                
            } catch (error) {
                Utils.log('ERROR', '获取资质数据失败', error.message);
                throw error;
            }
        },
        
        // 发送HTTP请求
        makeRequest: function(url, options) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: options.method || 'POST',
                    url: url,
                    headers: options.headers || {},
                    data: options.body || '',
                    timeout: 10000,
                    onload: function(response) {
                        try {
                            const data = JSON.parse(response.responseText);
                            resolve(data);
                        } catch (error) {
                            reject(new Error('响应数据解析失败'));
                        }
                    },
                    onerror: function(error) {
                        reject(new Error('网络请求失败'));
                    },
                    ontimeout: function() {
                        reject(new Error('请求超时'));
                    }
                });
            });
        },
        
        // 获取通用请求头
        getCommonHeaders: function() {
            return {
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "content-type": "application/json;charset=UTF-8",
                "dsm-client-info": '{"terminal":"0"}',
                "dsm-lang": "zh-CN",
                "dsm-platform": "pc",
                "dsm-site": "",
                "priority": "u=1, i",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "x-requested-with": "XMLHttpRequest"
            };
        },
        
        // 获取资质接口专用请求头
        getQualificationHeaders: function() {
            const headers = this.getCommonHeaders();
            headers["dsm-file-path"] = "lineation-price";
            return headers;
        },
        
        // 转换账户数据格式
        transformAccountData: function(data) {
            // 将所有扩展数据合并到一个对象中
            const extendedData = {
                currentBelongType: data.currentBelongType || '',
                belongVos: data.belongVos || [],
                isMainUser: data.mainUser || false,
                canSwitchIdentity: data.switchIdentity || false,
                businessFieldName: data.belongVos?.[0]?.businessFieldName || '',
                businessFieldCode: data.belongVos?.[0]?.businessFieldCode || '',
                scrapedAt: new Date().toISOString(),
                rawData: data // 原始数据
            };

            return {
                accountCode: data.venderId || '',
                accountName: data.pin || '',
                managerName: CONFIG.MANAGER_NAME || '', // 添加管理员姓名
                extendedData: JSON.stringify(extendedData) // 所有扩展数据作为JSON字符串
            };
        },
        
        // 转换资质数据格式
        transformQualificationData: function(item) {
            // 构建扩展信息，存储到remark字段
            const extendedInfo = {
                brandGrade: item.brandGrade || 0,
                brandGradeName: item.brandGradeName || '',
                brandType: item.brandType || 0,
                brandTypeDesc: item.brandTypeDesc || '',
                statusDesc: item.statusDesc || '',
                brandRelationId: item.brandRelationId || '',
                brandQuaId: item.brandQuaId || '',
                isChange: item.isChange || -1,
                scrapedAt: new Date().toISOString(),
                rawData: item
            };

            return {
                // vcAccount字段现在应该存储account_name，而不是account_code
                // 后台会根据这个值查找对应的vc_account记录并设置vcAccountId
                vcAccount: item.vendorCode || '', // 暂时使用vendorCode，后台会处理映射关系
                brand: item.brandName || '',
                brandCode: item.brandCode || '',
                // 将品牌等级信息映射到产品线分级字段
                productLineLevel: item.brandGradeName || '',
                firstCategory: item.brandSort1Name || '',
                firstCategoryCode: item.brandSort1Code || '',
                secondCategory: item.brandSort2Name || '',
                secondCategoryCode: item.brandSort2Code || '',
                thirdCategory: item.brandSort3Name || '',
                thirdCategoryCode: item.brandSort3Code || '',
                qualificationExpireDate: item.expiryDate ? Utils.formatDate(item.expiryDate) : '',
                purchaser: item.purchaserCode || '',
                purchaserName: item.purchaserName || '',
                // 将部门信息映射到一级部门字段
                firstDepartment: item.deptName || '',
                firstDepartmentCode: item.deptCode || '',
                // 使用品牌类型描述作为产品线类型
                productLineType: item.brandTypeDesc || '',
                status: item.status || 0,
                scraped_at: new Date().toISOString(),
                // 将所有扩展信息存储到备注字段（JSON格式）
                remark: `京麦同步数据: ${JSON.stringify(extendedInfo)}`
            };
        },
        
        // 模拟真实用户行为（已禁用，避免影响页面功能）
        simulateUserBehavior: function() {
            // 禁用所有用户行为模拟，避免干扰页面正常运行
            // Utils.log('INFO', '用户行为模拟已禁用');
        },

        // 扩展用户行为模拟（已禁用，避免影响页面功能）
        simulateExtendedUserBehavior: function() {
            // 禁用扩展用户行为模拟，避免干扰页面正常运行
            // Utils.log('INFO', '扩展用户行为模拟已禁用');
        }
    };
    
    // ===== API客户端模块 =====
    const ApiClient = {
        // 上传VC账户数据
        uploadAccountData: async function(accountData) {
            Utils.log('INFO', '开始上传VC账户数据');

            try {
                const response = await this.makeApiRequest('/vc-account', {
                    method: 'POST',
                    body: JSON.stringify(accountData)
                });

                // 保存同步结果，包含VC账号ID信息
                if (response && response.data) {
                    const syncResult = {
                        success: response.data.success,
                        entityId: response.data.entityId,
                        entityCode: response.data.entityCode,
                        accountCode: accountData.accountCode,
                        accountName: accountData.accountName,
                        syncTime: new Date().toISOString()
                    };

                    // 保存到本地存储，供后续资质同步使用
                    GM_setValue('vc_account_sync_result', syncResult);
                    Utils.log('INFO', 'VC账户同步结果已保存', syncResult);
                }

                Utils.log('INFO', 'VC账户数据上传成功', response);
                return response;
            } catch (error) {
                Utils.log('ERROR', 'VC账户数据上传失败', error.message);
                throw error;
            }
        },

        // 获取已同步的VC账号信息
        getSyncedVcAccountInfo: function() {
            const syncResult = GM_getValue('vc_account_sync_result', null);
            if (syncResult && syncResult.success && syncResult.entityId) {
                return {
                    vcAccountId: syncResult.entityId,
                    vcAccountCode: syncResult.entityCode,
                    accountName: syncResult.accountName,
                    syncTime: syncResult.syncTime
                };
            }
            return null;
        },

        // 批量上传资质数据
        batchUploadQualifications: async function(qualifications) {
            Utils.log('INFO', `开始批量上传资质数据，共${qualifications.length}条`);
            
            try {
                // 一次性发送所有数据
                const response = await this.makeApiRequest('/qualifications/batch', {
                    method: 'POST',
                    body: JSON.stringify({ 
                        qualifications: qualifications,  // 发送所有数据
                        syncMode: 2,  // 覆盖更新模式
                        dataSource: 'TAMPERMONKEY_SCRIPT'
                    })
                });
                
                Utils.log('INFO', `批量上传成功，响应:`, response);
                
                // 更新进度到100%
                UI.updateUploadProgress(qualifications.length, qualifications.length);
                
                // 返回上传成功的数量
                Utils.log('INFO', `API响应原始数据:`, response);
                
                // 即使部分失败，也要返回成功的数量
                const successCount = response && response.data && typeof response.data.successCount === 'number' 
                    ? response.data.successCount 
                    : 0; // 如果获取不到数量，返回0而不是发送数量
                
                Utils.log('INFO', `批量上传完成，成功数量: ${successCount}, 响应中的successCount: ${response?.data?.successCount}`);
                
                // 如果有错误信息，记录但不抛出异常
                if (response?.data?.errorMessage) {
                    Utils.log('WARN', `上传过程中有错误: ${response.data.errorMessage}`);
                }
                
                return successCount;
                
            } catch (error) {
                Utils.log('ERROR', `批量上传失败`, error.message);
                throw error;
            }
        },
        
        // 发送API请求
        makeApiRequest: function(endpoint, options = {}) {
            const url = CONFIG.API_BASE_URL + endpoint;
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.API_TOKEN}`,
                ...options.headers
            };
            
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: options.method || 'GET',
                    url: url,
                    headers: headers,
                    data: options.body || '',
                    timeout: 30000,
                    onload: function(response) {
                        try {
                            if (response.status >= 200 && response.status < 300) {
                                const data = JSON.parse(response.responseText);
                                resolve(data);
                            } else {
                                reject(new Error(`API请求失败: ${response.status} ${response.statusText}`));
                            }
                        } catch (error) {
                            reject(new Error('API响应解析失败'));
                        }
                    },
                    onerror: function(error) {
                        reject(new Error('API请求网络错误'));
                    },
                    ontimeout: function() {
                        reject(new Error('API请求超时'));
                    }
                });
            });
        },

        // 测试API连通性
        testConnection: async function() {
            Utils.log('INFO', '开始测试API连通性');
            
            try {
                const response = await this.makeApiRequest('/test', {
                    method: 'GET'
                });
                
                Utils.log('INFO', 'API连通性测试成功', response);
                return response;
            } catch (error) {
                Utils.log('ERROR', 'API连通性测试失败', error.message);
                throw error;
            }
        }
    };
    
    // ===== 用户界面模块 =====
    const UI = {
        panel: null,
        
        // 初始化界面
        init: function() {
            this.createPanel();
            this.bindEvents();
            this.updateStatistics(); // 添加统计信息更新
            this.updateSyncedVcInfo(); // 更新已同步VC账号信息显示
            Utils.log('INFO', '用户界面初始化完成');
        },
        
        // 创建操作面板
        createPanel: function() {
            // 检查是否为首次使用
            const isFirstTime = !CONFIG.FIRST_SETUP_DONE;
            const defaultMinimized = !isFirstTime; // 首次之外默认最小化
            
            const panelHtml = `
                <div id="jingmai-scraper-panel" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 260px;
                    background: #fff;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 10000;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                ">
                    <div style="
                        background: #f5f5f5;
                        padding: 8px 12px;
                        border-bottom: 1px solid #ddd;
                        border-radius: 6px 6px 0 0;
                        cursor: move;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <span style="font-weight: bold; font-size: 12px;">京麦数据抓取</span>
                        <span id="minimize-btn" style="cursor: pointer; font-weight: bold; width: 16px; height: 16px; text-align: center; line-height: 16px; background: #007cff; color: white; border-radius: 2px; font-size: 10px;">${defaultMinimized ? '+' : '−'}</span>
                    </div>
                    <div id="panel-content" style="padding: 12px; ${defaultMinimized ? 'display: none;' : ''}">
                        <div style="margin-bottom: 12px;">
                            <div style="margin-bottom: 4px; font-size: 11px;"><strong>页面:</strong> <span id="page-type">检测中...</span></div>
                            <div style="margin-bottom: 4px; font-size: 11px;"><strong>状态:</strong> <span id="scraper-status">就绪</span></div>
                        </div>
                        
                        <div style="margin-bottom: 12px;">
                            <button id="scrape-account-btn" style="
                                width: 100%;
                                padding: 6px;
                                margin-bottom: 4px;
                                background: #007cff;
                                color: white;
                                border: none;
                                border-radius: 3px;
                                cursor: pointer;
                                font-size: 11px;
                            ">抓取VC账户</button>
                            
                            <button id="scrape-qualifications-btn" style="
                                width: 100%;
                                padding: 6px;
                                margin-bottom: 4px;
                                background: #28a745;
                                color: white;
                                border: none;
                                border-radius: 3px;
                                cursor: pointer;
                                font-size: 11px;
                            ">抓取资质数据</button>
                            
                            <button id="upload-data-btn" style="
                                width: 100%;
                                padding: 6px;
                                margin-bottom: 8px;
                                background: #ffc107;
                                color: #000;
                                border: none;
                                border-radius: 3px;
                                cursor: pointer;
                                font-size: 11px;
                            ">上传到系统</button>
                            
                            <div style="display: flex; gap: 4px;">
                                <button id="settings-btn" style="
                                    flex: 1;
                                    padding: 6px;
                                    background: #17a2b8;
                                    color: white;
                                    border: none;
                                    border-radius: 3px;
                                    cursor: pointer;
                                    font-size: 10px;
                                ">设置</button>
                                <button id="reset-btn" style="
                                    flex: 1;
                                    padding: 6px;
                                    background: #dc3545;
                                    color: white;
                                    border: none;
                                    border-radius: 3px;
                                    cursor: pointer;
                                    font-size: 10px;
                                ">重置</button>
                            </div>
                        </div>
                        
                        <div id="progress-section" style="margin-bottom: 12px; display: none;">
                            <div style="margin-bottom: 4px; font-size: 11px;"><strong>进度:</strong></div>
                            <div style="
                                width: 100%;
                                height: 16px;
                                background: #f0f0f0;
                                border-radius: 8px;
                                overflow: hidden;
                            ">
                                <div id="progress-bar" style="
                                    height: 100%;
                                    background: #007cff;
                                    width: 0%;
                                    transition: width 0.3s;
                                "></div>
                            </div>
                            <div id="progress-text" style="text-align: center; margin-top: 4px; font-size: 10px;">0%</div>
                        </div>
                        
                        <div style="font-size: 10px;">
                            <div style="margin-bottom: 3px; font-weight: bold; color: #666;">统计信息:</div>
                            <div style="color: #666;">
                                <div>VC账户: <span id="account-count">0</span></div>
                                <div>资质数据: <span id="qualification-count">0</span></div>
                                <div>上传成功: <span id="upload-success-count">0</span></div>
                                <div id="synced-vc-info" style="margin-top: 4px; color: #28a745; display: none; font-size: 9px;">
                                    VC账号: <span id="synced-vc-name"></span> (ID: <span id="synced-vc-id"></span>)
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(panelHtml);
            this.panel = $('#jingmai-scraper-panel');
            
            // 更新页面类型显示
            const pageType = Utils.getPageType();
            $('#page-type').text(pageType === 'account' ? 'VC账户' : 
                                pageType === 'qualification' ? '资质管理' : '未知');
        },
        
        // 绑定事件
        bindEvents: function() {
            // 最小化/展开面板
            $('#minimize-btn').click(() => {
                const content = $('#panel-content');
                const btn = $('#minimize-btn');
                if (content.is(':visible')) {
                    content.hide();
                    btn.text('+');
                } else {
                    content.show();
                    btn.text('−');
                }
            });
            
            // 抓取VC账户信息
            $('#scrape-account-btn').click(async () => {
                await this.handleScrapeAccount();
            });
            
            // 抓取资质数据
            $('#scrape-qualifications-btn').click(async () => {
                await this.handleScrapeQualifications();
            });
            
            // 上传数据
            $('#upload-data-btn').click(async () => {
                await this.handleUploadData();
            });
            
            // 设置按钮
            $('#settings-btn').click(() => {
                this.showSettingsDialog();
            });

            // 重置按钮
            $('#reset-btn').click(() => {
                Main.resetToFirstSetup();
            });
            
            // 拖拽功能
            this.makeDraggable();
        },
        
        // 处理抓取VC账户信息
        handleScrapeAccount: async function() {
            this.setStatus('正在抓取VC账户信息...');
            this.disableButtons();
            
            try {
                const accountData = await DataScraper.getAccountInfo();
                GM_setValue('scraped_account', accountData);
                
                this.updateStatistics(); // 更新统计信息
                this.setStatus('VC账户信息抓取完成');
                GM_notification('VC账户信息抓取成功', 'success');
                
                // 检查是否需要自动上传（非首次使用）
                if (CONFIG.FIRST_SETUP_DONE) {
                    Utils.log('INFO', '非首次使用，启动自动上传VC账户信息');
                    setTimeout(async () => {
                        await this.autoUploadAccount(accountData);
                    }, 2000); // 延迟2秒后开始自动上传
                }
                
            } catch (error) {
                this.setStatus('VC账户信息抓取失败');
                GM_notification('VC账户信息抓取失败: ' + error.message, 'error');
            } finally {
                this.enableButtons();
            }
        },

        // 自动上传VC账户信息（仅非首次使用时）
        autoUploadAccount: async function(accountData) {
            Utils.log('INFO', '开始自动上传VC账户信息');
            this.setStatus('自动上传VC账户信息...');
            this.disableButtons();
            
            try {
                const uploadResult = await ApiClient.uploadAccountData(accountData);
                
                if (uploadResult && uploadResult.data) {
                    const result = uploadResult.data;
                    if (result.success && result.entityId) {
                        Utils.log('INFO', `VC账户同步成功: ID=${result.entityId}`);
                        this.setStatus(`VC账户自动上传完成 (ID: ${result.entityId})`);

                        // 更新已同步VC账号信息显示
                        this.updateSyncedVcInfo();

                        // 显示简单完成通知
                        this.showSimpleNotification('VC账户信息已自动同步到系统', 'success');
                        
                        // 保存VC账户上传成功的日期
                        GM_setValue('last_account_upload_time', new Date().toDateString());
                    }
                }
                
                this.updateStatistics();
                
            } catch (error) {
                this.setStatus('VC账户自动上传失败');
                Utils.log('ERROR', 'VC账户自动上传失败', error.message);
                GM_notification('VC账户自动上传失败: ' + error.message, 'error');
            } finally {
                this.enableButtons();
            }
        },

        // 显示简单通知（用于VC账户上传完成）
        showSimpleNotification: function(message, type = 'success') {
            const bgColor = type === 'success' ? '#28a745' : '#dc3545';
            const icon = type === 'success' ? '✓' : '✕';
            
            const notificationHtml = `
                <div id="simple-notification" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 300px;
                    background: ${bgColor};
                    color: white;
                    border-radius: 8px;
                    padding: 15px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                    z-index: 10002;
                    font-family: Arial, sans-serif;
                    display: flex;
                    align-items: center;
                    animation: slideIn 0.3s ease-out;
                ">
                    <div style="
                        width: 24px;
                        height: 24px;
                        background: rgba(255,255,255,0.2);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 12px;
                        font-weight: bold;
                    ">${icon}</div>
                    <div style="flex: 1; font-size: 14px;">${message}</div>
                    <span id="simple-notification-close" style="
                        cursor: pointer;
                        font-weight: bold;
                        margin-left: 8px;
                        opacity: 0.7;
                    ">×</span>
                </div>
                <style>
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                </style>
            `;

            // 移除之前的通知
            $('#simple-notification').remove();
            $('body').append(notificationHtml);

            // 绑定关闭事件
            $('#simple-notification-close').click(() => {
                $('#simple-notification').remove();
            });

            // 3秒后自动消失
            setTimeout(() => {
                $('#simple-notification').remove();
            }, 3000);
        },
        
        // 处理抓取资质数据 - 手动拦截模式
        handleScrapeQualifications: async function() {
            this.setStatus('正在准备手动抓取模式...');
            this.disableButtons();

            try {
                // 检查是否在正确的页面
                const pageType = Utils.getPageType();
                if (pageType !== 'qualification') {
                    throw new Error('请先导航到资质管理页面');
                }

                // 检查拦截状态
            const interceptStatus = DataScraper.checkInterceptStatus();
            console.group('🔍 [拦截状态检查]');
            console.log('当前拦截状态:', interceptStatus);
            console.log('页面URL:', window.location.href);
                console.log('页面类型:', pageType);
            console.groupEnd();

                // 如果没有拦截到数据，给用户提示
                if (!interceptStatus.hasCachedData) {
                    this.setStatus('等待数据拦截中...');
                    
                    // 显示操作指南
                    this.showManualInterceptGuide();
                    
                    // 等待用户操作和数据拦截
                    const maxWaitTime = 60000; // 最多等待60秒
                    const startTime = Date.now();
                    
                    while (Date.now() - startTime < maxWaitTime) {
                        const currentStatus = DataScraper.checkInterceptStatus();
                        if (currentStatus.hasCachedData) {
                            console.log('✅ 检测到拦截数据，开始处理');
                            break;
                        }
                        await Utils.delay(2000); // 每2秒检查一次
                        this.setStatus(`等待数据拦截中... (${Math.round((Date.now() - startTime)/1000)}s)`);
                    }
                }

                // 获取拦截到的资质数据
                const qualifications = await DataScraper.getAllQualifications();
                
                // 保存数据
                GM_setValue('scraped_qualifications', qualifications);
                
                this.updateStatistics();
                this.setStatus(`资质数据抓取完成，共${qualifications.length}条`);
                
                console.log(`✅ 资质数据抓取完成，共${qualifications.length}条记录`);
                GM_notification('资质数据抓取成功', `已获取${qualifications.length}条资质数据`, 'success');
                
                // 检查是否需要自动上传（非首次使用）
                if (CONFIG.FIRST_SETUP_DONE && CONFIG.AUTO_SCRAPE) {
                    Utils.log('INFO', '自动模式开启，开始自动上传资质数据');
                    setTimeout(async () => {
                        await this.autoUploadAfterScrape(qualifications);
                    }, 2000);
                }
                
            } catch (error) {
                this.setStatus('资质数据抓取失败');
                console.error('资质数据抓取失败:', error);
                GM_notification('资质数据抓取失败: ' + error.message, 'error');
                
                // 如果是需要手动操作的错误，显示友好提示
                if (error.message.includes('手动操作') || error.message.includes('翻页')) {
                    this.showManualInterceptGuide();
                }
            } finally {
                this.enableButtons();
            }
        },

        // 显示手动拦截指南
        showManualInterceptGuide: function() {
            const guideHtml = `
                <div id="manual-guide" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 450px;
                    background: white;
                    border: 2px solid #007cff;
                    border-radius: 12px;
                    padding: 25px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                    text-align: left;
                ">
                    <div style="position: relative; margin-bottom: 20px;">
                        <div style="
                            width: 60px;
                            height: 60px;
                            background: #007cff;
                            border-radius: 50%;
                            margin: 0 auto 15px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 30px;
                            color: white;
                        ">👋</div>
                        <h3 style="margin: 0; color: #007cff; font-size: 20px; text-align: center;">手动抓取指南</h3>
                        <span id="guide-close-btn" style="
                            position: absolute;
                            top: -10px;
                            right: -10px;
                            width: 25px;
                            height: 25px;
                            background: #dc3545;
                            color: white;
                            border-radius: 50%;
                            text-align: center;
                            line-height: 25px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 16px;
                        ">×</span>
                    </div>

                    <div style="margin-bottom: 20px; color: #666; line-height: 1.6;">
                        <p style="margin-bottom: 15px;">脚本已启用<strong>手动拦截模式</strong>，需要您配合操作来获取数据：</p>
                        
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <div style="font-weight: bold; color: #007cff; margin-bottom: 8px;">📋 操作步骤：</div>
                            <div style="color: #333; font-size: 14px;">
                                1. 在页面上进行<strong>翻页操作</strong><br>
                                2. 使用页面的<strong>筛选功能</strong><br>
                                3. 或者<strong>刷新页面</strong>重新加载数据<br>
                                4. 脚本会自动拦截API请求并获取数据
                            </div>
                        </div>
                        
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <div style="font-weight: bold; color: #1976d2; margin-bottom: 8px;">💡 温馨提示：</div>
                            <div style="color: #1565c0; font-size: 14px;">
                                • 这种方式<strong>避免了风控检测</strong><br>
                                • 只需要正常操作，脚本会在后台工作<br>
                                • 拦截到数据后会自动处理和上传
                            </div>
                        </div>
                        
                        <div style="font-size: 12px; color: #999; text-align: center;">
                            完成操作后请关闭此窗口
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button id="guide-ok" style="
                            padding: 10px 20px;
                            background: #007cff;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                        ">我知道了</button>
                    </div>
                </div>
            `;

            // 如果已经有指南窗口，先移除
            $('#manual-guide').remove();
            
            $('body').append(guideHtml);

            // 绑定事件
            $('#guide-close-btn, #guide-ok').click(() => {
                $('#manual-guide').remove();
            });
        },

        // 显示频率限制友好提示
        showFrequencyLimitNotification: function() {
            // 检查是否已经打开了频率限制提示
            if ($('#frequency-limit-notification').length > 0) {
                return;
            }
            
            const notificationHtml = `
                <div id="frequency-limit-notification" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 450px;
                    background: white;
                    border: 2px solid #ff9800;
                    border-radius: 12px;
                    padding: 25px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                    text-align: center;
                ">
                    <div style="position: relative; margin-bottom: 20px;">
                        <div style="
                            width: 60px;
                            height: 60px;
                            background: #ff9800;
                            border-radius: 50%;
                            margin: 0 auto 15px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 30px;
                            color: white;
                        ">⚠</div>
                        <h3 style="margin: 0; color: #ff9800; font-size: 20px;">请求频率限制</h3>
                        <span id="frequency-notification-close" style="
                            position: absolute;
                            top: -10px;
                            right: -10px;
                            width: 25px;
                            height: 25px;
                            background: #dc3545;
                            color: white;
                            border-radius: 50%;
                            text-align: center;
                            line-height: 25px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 16px;
                        ">×</span>
                    </div>

                    <div style="margin-bottom: 20px; color: #666; line-height: 1.6;">
                        <p style="margin-bottom: 15px;">京麦服务器检测到请求过于频繁，暂时限制了访问。</p>
                        
                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <div style="font-weight: bold; color: #856404; margin-bottom: 8px;">建议解决方案：</div>
                            <div style="text-align: left; color: #856404; font-size: 14px;">
                                • 等待 <strong>5-10 分钟</strong> 后重试<br>
                                • 避免在短时间内多次点击抓取按钮<br>
                                • 如有部分数据已获取，可先上传已有数据<br>
                                • 可尝试刷新页面后重新开始
                            </div>
                        </div>
                        
                        <div style="font-size: 12px; color: #999;">
                            脚本已自动增加延迟和重试机制，但京麦服务器仍有严格的频率限制
                        </div>
                    </div>

                    <div>
                        <button id="frequency-notification-ok" style="
                            padding: 10px 20px;
                            background: #ff9800;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                            margin-right: 10px;
                        ">知道了</button>
                        <button id="frequency-check-data" style="
                            padding: 10px 20px;
                            background: #28a745;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                        ">查看已获取数据</button>
                    </div>
                </div>
            `;

            $('body').append(notificationHtml);

            // 绑定事件
            $('#frequency-notification-close, #frequency-notification-ok').click(() => {
                this.closeFrequencyLimitNotification();
            });
            
            $('#frequency-check-data').click(() => {
                this.closeFrequencyLimitNotification();
                this.updateStatistics(); // 更新统计显示
                this.showSimpleNotification('已刷新数据统计，请查看面板中的统计信息', 'info');
            });
        },

        // 关闭频率限制通知
        closeFrequencyLimitNotification: function() {
            $('#frequency-limit-notification').remove();
        },

        // 自动上传抓取的数据（仅非首次使用时）
        autoUploadAfterScrape: async function(qualifications) {
            Utils.log('INFO', '开始自动上传抓取的数据');
            this.setStatus('自动上传数据中...');
            this.disableButtons();
            this.showProgress();
            
            try {
                let uploadCount = 0;
                
                // 上传VC账户数据
                const accountData = GM_getValue('scraped_account', null);
                if (accountData) {
                    const uploadResult = await ApiClient.uploadAccountData(accountData);
                    uploadCount++;

                    if (uploadResult && uploadResult.data) {
                        const result = uploadResult.data;
                        if (result.success && result.entityId) {
                            Utils.log('INFO', `VC账户同步成功: ID=${result.entityId}`);
                            // 更新已同步VC账号信息显示
                            this.updateSyncedVcInfo();
                            // 保存VC账户上传成功的日期
                            GM_setValue('last_account_upload_time', new Date().toDateString());
                        }
                    }
                }
                
                // 上传资质数据
                if (qualifications.length > 0) {
                    try {
                        const qualificationSuccessCount = await ApiClient.batchUploadQualifications(qualifications);
                        Utils.log('INFO', `资质上传返回的成功数量: ${qualificationSuccessCount}`);
                        
                        if (qualificationSuccessCount !== null && qualificationSuccessCount !== undefined && !isNaN(qualificationSuccessCount)) {
                            uploadCount += qualificationSuccessCount;
                        } else {
                            Utils.log('WARN', `资质上传返回无效数量: ${qualificationSuccessCount}, 使用默认值: ${qualifications.length}`);
                            uploadCount += qualifications.length; // 使用发送数量作为后备
                        }
                    } catch (error) {
                        Utils.log('ERROR', '资质数据上传失败', error.message);
                        // 上传失败但不中断流程，使用0作为上传成功数
                        uploadCount += 0;
                    }
                }
                
                this.updateCount('upload-success-count', uploadCount);
                this.updateStatistics();
                this.setStatus(`自动上传完成，成功${uploadCount}条`);
                
                // 保存上传成功数量到存储
                GM_setValue('upload_success_count', uploadCount);
                
                // 保存上传成功的日期
                GM_setValue('last_upload_time', new Date().toDateString());
                
                // 显示完成通知页面
                this.showCompletionNotification(uploadCount, qualifications.length);
                
                Utils.log('INFO', `自动上传完成: 成功${uploadCount}条`);
                
            } catch (error) {
                this.setStatus('自动上传失败');
                Utils.log('ERROR', '自动上传失败', error.message);
                GM_notification('自动上传失败: ' + error.message, 'error');
            } finally {
                this.enableButtons();
                this.hideProgress();
            }
        },

        // 显示完成通知页面
        showCompletionNotification: function(uploadCount, scrapedCount) {
            // 检查是否已经打开了通知对话框，避免重复打开
            if ($('#completion-notification').length > 0) {
                return;
            }
            
            const notificationHtml = `
                <div id="completion-notification" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 400px;
                    background: white;
                    border: 2px solid #28a745;
                    border-radius: 12px;
                    padding: 25px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                    text-align: center;
                ">
                    <div style="position: relative; margin-bottom: 20px;">
                        <div style="
                            width: 60px;
                            height: 60px;
                            background: #28a745;
                            border-radius: 50%;
                            margin: 0 auto 15px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 30px;
                            color: white;
                        ">✓</div>
                        <h3 style="margin: 0; color: #28a745; font-size: 20px;">数据同步完成</h3>
                        <span id="notification-close-btn" style="
                            position: absolute;
                            top: -10px;
                            right: -10px;
                            width: 25px;
                            height: 25px;
                            background: #dc3545;
                            color: white;
                            border-radius: 50%;
                            text-align: center;
                            line-height: 25px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 16px;
                        ">×</span>
                    </div>

                    <div style="margin-bottom: 20px; color: #666;">
                        <div style="margin-bottom: 8px; font-size: 14px;">
                            <strong>抓取数据：</strong> ${scrapedCount} 条资质记录
                        </div>
                        <div style="margin-bottom: 8px; font-size: 14px;">
                            <strong>上传成功：</strong> ${uploadCount} 条记录
                        </div>
                        <div style="font-size: 12px; color: #999;">
                            同步时间：${new Date().toLocaleString()}
                        </div>
                    </div>

                    <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 12px; color: #666;">
                        <div style="margin-bottom: 5px;"><strong>完成操作：</strong></div>
                        <div style="text-align: left;">
                            • 已抓取京麦资质数据并保存到本地<br>
                            • 已自动同步数据到资质管理系统<br>
                            • 数据已按照VC账号+品牌+分类去重处理<br>
                            • 下次访问将跳过重复数据的抓取
                        </div>
                    </div>

                    <div>
                        <button id="notification-ok" style="
                            padding: 10px 20px;
                            background: #28a745;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                            margin-right: 10px;
                        ">确定</button>
                        <button id="notification-view-logs" style="
                            padding: 10px 20px;
                            background: #17a2b8;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                        ">查看日志</button>
                    </div>
                </div>
            `;

            $('body').append(notificationHtml);

            // 绑定事件
            $('#notification-close-btn, #notification-ok').click(() => {
                this.closeCompletionNotification();
            });
            
            $('#notification-view-logs').click(() => {
                this.closeCompletionNotification();
                this.showLogsDialog();
            });

            // 播放通知声音（如果浏览器支持）
            try {
                GM_notification('京麦数据同步完成', `成功同步${uploadCount}条记录到系统`, 'success');
            } catch (e) {
                // 静默处理通知错误
            }
        },

        // 关闭完成通知
        closeCompletionNotification: function() {
            $('#completion-notification').remove();
        },
        
        // 处理数据上传
        handleUploadData: async function() {
            this.setStatus('正在上传数据...');
            this.disableButtons();
            this.showProgress();
            
            try {
                let uploadCount = 0;
                
                // 上传VC账户数据
                const accountData = GM_getValue('scraped_account', null);
                if (accountData) {
                    const uploadResult = await ApiClient.uploadAccountData(accountData);
                    uploadCount++;

                    // 显示详细的上传结果
                    if (uploadResult && uploadResult.data) {
                        const result = uploadResult.data;
                        if (result.success && result.entityId) {
                            Utils.log('INFO', `VC账户同步成功: ID=${result.entityId}, Code=${result.entityCode}`);
                            this.setStatus(`VC账户同步成功 (ID: ${result.entityId})`);
                            // 更新已同步VC账号信息显示
                            this.updateSyncedVcInfo();
                            // 保存VC账户上传成功的日期
                            GM_setValue('last_account_upload_time', new Date().toDateString());
                        }
                    }
                }
                
                // 启用资质数据上传，上传所有数据
                const qualifications = GM_getValue('scraped_qualifications', []);
                if (qualifications.length > 0) {
                    const qualificationSuccessCount = await ApiClient.batchUploadQualifications(qualifications);
                    uploadCount += qualificationSuccessCount;
                }
                
                this.updateCount('upload-success-count', uploadCount);
                this.updateStatistics(); // 更新统计信息
                this.setStatus(`数据上传完成，成功${uploadCount}条`);
                
                // 保存上传成功数量到存储
                GM_setValue('upload_success_count', uploadCount);
                
                // 保存上传成功的日期  
                GM_setValue('last_upload_time', new Date().toDateString());
                
                GM_notification(`数据上传成功，共${uploadCount}条`, 'success');
                
            } catch (error) {
                this.setStatus('数据上传失败');
                GM_notification('数据上传失败: ' + error.message, 'error');
            } finally {
                this.enableButtons();
                this.hideProgress();
            }
        },

        // 显示设置对话框
        showSettingsDialog: function() {
            // 检查是否已经打开了设置对话框，避免重复打开
            if ($('#settings-dialog').length > 0) {
                return;
            }
            
            const settingsHtml = `
                <div id="settings-dialog" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 450px;
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                ">
                    <div style="position: relative; margin-bottom: 15px;">
                        <h3 style="margin: 0; color: #333;">京麦数据同步 - 设置</h3>
                        <span id="settings-close-btn" style="
                            position: absolute;
                            top: -5px;
                            right: -5px;
                            width: 25px;
                            height: 25px;
                            background: #dc3545;
                            color: white;
                            border-radius: 50%;
                            text-align: center;
                            line-height: 25px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 16px;
                        ">×</span>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #888;">API服务器地址: (不可修改)</label>
                        <input id="settings-api-url" type="text" value="${CONFIG.API_BASE_URL}" readonly disabled style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                            background-color: #f5f5f5;
                            color: #888;
                            cursor: not-allowed;
                        ">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">管理员姓名:</label>
                        <input id="settings-manager-name" type="text" value="${CONFIG.MANAGER_NAME}" placeholder="请输入管理员姓名" style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        ">
                        <small style="color: #666; margin-top: 5px; display: block;">
                            此姓名将用于匹配系统中的用户，作为VC账户的负责人
                        </small>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: flex; align-items: center;">
                            <input id="settings-auto-sync" type="checkbox" ${CONFIG.AUTO_SCRAPE ? 'checked' : ''} style="margin-right: 8px;">
                            <span>启用自动同步</span>
                        </label>
                        <small style="color: #666; margin-left: 20px;">
                            开启后，访问京麦页面时会自动同步数据到系统
                        </small>
                    </div>

                    <div style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">当前状态:</div>
                        <div style="font-size: 12px;">
                            <div>✓ 首次设置: ${CONFIG.FIRST_SETUP_DONE ? '已完成' : '未完成'}</div>
                            <div>✓ API地址: ${CONFIG.API_BASE_URL || '未设置'}</div>
                            <div>✓ 管理员: ${CONFIG.MANAGER_NAME || '未设置'}</div>
                            <div>✓ 自动同步: ${CONFIG.AUTO_SCRAPE ? '已启用' : '已禁用'}</div>
                        </div>
                    </div>

                    <div style="display: flex; flex-wrap: wrap; justify-content: flex-end; gap: 8px; align-items: center;">
                        <button id="settings-test-connection" style="
                            padding: 6px 12px;
                            background: #28a745;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">测试连接</button>
                        <button id="settings-test-manager" style="
                            padding: 6px 12px;
                            background: #17a2b8;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">测试管理员</button>
                        <button id="settings-logs" style="
                            padding: 6px 12px;
                            background: #ffc107;
                            color: #000;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">查看日志</button>
                        <button id="settings-cancel" style="
                            padding: 6px 12px;
                            background: #6c757d;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">取消</button>
                        <button id="settings-save" style="
                            padding: 6px 12px;
                            background: #007cff;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">保存</button>
                    </div>

                    <div id="settings-status" style="
                        margin-top: 15px;
                        padding: 8px;
                        border-radius: 4px;
                        display: none;
                    "></div>
                </div>
            `;

            $('body').append(settingsHtml);

            // 绑定事件
            $('#settings-close-btn').click(() => this.closeSettingsDialog());
            $('#settings-test-connection').click(() => this.testConnectionInSettings());
            $('#settings-test-manager').click(() => this.testManagerInSettings());
            $('#settings-logs').click(() => this.showLogsDialog());
            $('#settings-cancel').click(() => this.closeSettingsDialog());
            $('#settings-save').click(() => this.saveSettings());
        },

        // 在设置中测试连接
        testConnectionInSettings: function() {
            $('#settings-status').show().html('正在测试连接...').css({
                'background': '#e3f2fd',
                'color': '#1976d2',
                'border': '1px solid #bbdefb'
            });

            GM_xmlhttpRequest({
                method: 'GET',
                url: CONFIG.API_BASE_URL + '/test',
                timeout: 5000,
                onload: function(response) {
                    if (response.status === 200) {
                        $('#settings-status').html('✅ 连接成功！服务器响应正常').css({
                            'background': '#e8f5e8',
                            'color': '#2e7d32',
                            'border': '1px solid #c8e6c9'
                        });
                    } else {
                        $('#settings-status').html('❌ 连接失败：服务器响应异常').css({
                            'background': '#ffebee',
                            'color': '#c62828',
                            'border': '1px solid #ffcdd2'
                        });
                    }
                },
                onerror: function() {
                    $('#settings-status').html('❌ 连接失败：无法连接到服务器').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                },
                ontimeout: function() {
                    $('#settings-status').html('❌ 连接超时：请检查服务器地址').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                }
            });
        },

        // 在设置中测试管理员
        testManagerInSettings: function() {
            const managerName = $('#settings-manager-name').val().trim();
            if (!managerName) {
                $('#settings-status').show().html('❌ 请先输入管理员姓名').css({
                    'background': '#ffebee',
                    'color': '#c62828',
                    'border': '1px solid #ffcdd2'
                });
                return;
            }

            $('#settings-status').show().html('正在测试管理员匹配...').css({
                'background': '#e3f2fd',
                'color': '#1976d2',
                'border': '1px solid #bbdefb'
            });

            GM_xmlhttpRequest({
                method: 'GET',
                url: CONFIG.API_BASE_URL + '/test-manager?managerName=' + encodeURIComponent(managerName),
                timeout: 5000,
                onload: function(response) {
                    if (response.status === 200) {
                        try {
                            const result = JSON.parse(response.responseText);
                            if (result.code === 0 && result.data) {
                                $('#settings-status').html(`✅ 管理员匹配成功！用户ID: ${result.data.userId}, 姓名: ${result.data.nickname}`).css({
                                    'background': '#e8f5e8',
                                    'color': '#2e7d32',
                                    'border': '1px solid #c8e6c9'
                                });
                            } else {
                                $('#settings-status').html('❌ 未找到匹配的管理员，请检查姓名是否正确').css({
                                    'background': '#fff3e0',
                                    'color': '#f57c00',
                                    'border': '1px solid #ffcc02'
                                });
                            }
                        } catch (e) {
                            $('#settings-status').html('❌ 响应数据解析失败').css({
                                'background': '#ffebee',
                                'color': '#c62828',
                                'border': '1px solid #ffcdd2'
                            });
                        }
                    } else {
                        $('#settings-status').html('❌ 管理员匹配测试失败：服务器响应异常').css({
                            'background': '#ffebee',
                            'color': '#c62828',
                            'border': '1px solid #ffcdd2'
                        });
                    }
                },
                onerror: function() {
                    $('#settings-status').html('❌ 管理员匹配测试失败：无法连接到服务器').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                },
                ontimeout: function() {
                    $('#settings-status').html('❌ 管理员匹配测试超时：请检查服务器地址').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                }
            });
        },

        // 保存设置
        saveSettings: function() {
            // API地址不允许修改，所以不保存
            const managerName = $('#settings-manager-name').val().trim();
            const autoSync = $('#settings-auto-sync').is(':checked');

            // 保存配置（不保存API_BASE_URL，因为它是统一管理的）
            GM_setValue('MANAGER_NAME', managerName);
            GM_setValue('AUTO_SCRAPE', autoSync);

            // 更新当前配置（API地址保持不变）
            CONFIG.MANAGER_NAME = managerName;
            CONFIG.AUTO_SCRAPE = autoSync;

            // 关闭对话框
            this.closeSettingsDialog();

            // 显示成功消息
            GM_notification('设置已保存', '配置更新成功', 'success');
        },

        // 关闭设置对话框
        closeSettingsDialog: function() {
            $('#settings-dialog').remove();
        },

        // 显示日志对话框
        showLogsDialog: function() {
            const logs = GM_getValue('scrape_logs', []);
            const logsHtml = logs.slice(-50).reverse().map(log => 
                `<div style="margin-bottom: 8px; padding: 6px; background: ${
                    log.level === 'ERROR' ? '#fff5f5' : 
                    log.level === 'WARN' ? '#fffbf0' : '#f8f9fa'
                }; border-left: 3px solid ${
                    log.level === 'ERROR' ? '#dc3545' : 
                    log.level === 'WARN' ? '#ffc107' : '#007cff'
                }; font-size: 11px;">
                    <div style="font-weight: bold; color: #666;">[${log.timestamp.split('T')[1].split('.')[0]}] ${log.level}</div>
                    <div style="margin-top: 2px;">${log.message}</div>
                    ${log.data ? `<div style="margin-top: 2px; color: #666; font-family: monospace;">${JSON.stringify(log.data, null, 2).substring(0, 200)}...</div>` : ''}
                </div>`
            ).join('');
            
            const dialogHtml = `
                <div id="logs-dialog" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 600px;
                    max-width: 90vw;
                    height: 500px;
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                ">
                    <div style="padding: 15px; border-bottom: 1px solid #ddd;">
                        <strong>运行日志</strong>
                        <span id="close-logs" style="float: right; cursor: pointer; font-weight: bold;">×</span>
                        <button id="clear-logs" style="
                            float: right;
                            margin-right: 15px;
                            padding: 4px 8px;
                            background: #dc3545;
                            color: white;
                            border: none;
                            border-radius: 3px;
                            cursor: pointer;
                            font-size: 11px;
                        ">清空日志</button>
                    </div>
                    <div style="padding: 15px; height: 400px; overflow-y: auto;">
                        ${logsHtml || '<div style="text-align: center; color: #666; margin-top: 50px;">暂无日志</div>'}
                    </div>
                </div>
            `;
            
            $('body').append(dialogHtml);
            
            // 绑定事件
            $('#close-logs').click(() => {
                $('#logs-dialog').remove();
            });
            
            $('#clear-logs').click(() => {
                GM_setValue('scrape_logs', []);
                $('#logs-dialog').remove();
                GM_notification('日志已清空', 'success');
            });
        },
        
        // 工具方法
        setStatus: function(status) {
            $('#scraper-status').text(status);
        },

        // 更新已同步VC账号信息显示
        updateSyncedVcInfo: function() {
            const vcInfo = ApiClient.getSyncedVcAccountInfo();
            if (vcInfo) {
                $('#synced-vc-name').text(vcInfo.accountName);
                $('#synced-vc-id').text(vcInfo.vcAccountId);
                $('#synced-vc-info').show();
            } else {
                $('#synced-vc-info').hide();
            }
        },

        updateCount: function(elementId, count) {
            $(`#${elementId}`).text(count);
        },
        
        updateProgress: function(current, total) {
            const percent = Math.round((current / total) * 100);
            $('#progress-bar').css('width', percent + '%');
            $('#progress-text').text(`${percent}% (${current}/${total})`);
        },
        
        updateUploadProgress: function(current, total) {
            this.updateProgress(current, total);
        },
        
        showProgress: function() {
            $('#progress-section').show();
            this.updateProgress(0, 100);
        },
        
        hideProgress: function() {
            $('#progress-section').hide();
        },
        
        disableButtons: function() {
            $('#scrape-account-btn, #scrape-qualifications-btn, #upload-data-btn').prop('disabled', true);
        },
        
        enableButtons: function() {
            $('#scrape-account-btn, #scrape-qualifications-btn, #upload-data-btn').prop('disabled', false);
        },
        
        makeDraggable: function() {
            let isDragging = false;
            let startX, startY, startLeft, startTop;
            
            this.panel.find('div:first').on('mousedown', function(e) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                const offset = $('#jingmai-scraper-panel').offset();
                startLeft = offset.left;
                startTop = offset.top;
                
                $(document).on('mousemove', function(e) {
                    if (isDragging) {
                        const newLeft = startLeft + (e.clientX - startX);
                        const newTop = startTop + (e.clientY - startY);
                        $('#jingmai-scraper-panel').css({
                            left: newLeft + 'px',
                            top: newTop + 'px',
                            right: 'auto'
                        });
                    }
                });
                
                $(document).on('mouseup', function() {
                    isDragging = false;
                    $(document).off('mousemove mouseup');
                });
            });
        },
        
        // 更新统计信息
        updateStatistics: function() {
            // 更新VC账户统计
            const accountData = GM_getValue('scraped_account', null);
            const accountCount = accountData ? 1 : 0;
            this.updateCount('account-count', accountCount);
            
            // 更新资质数据统计
            const qualifications = GM_getValue('scraped_qualifications', []);
            const qualificationCount = Array.isArray(qualifications) ? qualifications.length : 0;
            this.updateCount('qualification-count', qualificationCount);
            
            // 显示已同步的VC账号信息
            const syncedVcInfo = ApiClient.getSyncedVcAccountInfo();
            if (syncedVcInfo) {
                $('#synced-vc-info').show();
                $('#synced-vc-name').text(syncedVcInfo.accountName || syncedVcInfo.vcAccountCode);
                $('#synced-vc-id').text(syncedVcInfo.vcAccountId);
            } else {
                $('#synced-vc-info').hide();
            }
            
            Utils.log('INFO', `统计信息更新: VC账户=${accountCount}, 资质数据=${qualificationCount}`);
        }
    };
    
    // ===== 主控制模块 =====
    const Main = {
        // 启动应用
        init: function() {
            Utils.log('INFO', '京麦数据抓取脚本开始初始化');

            // 等待页面加载完成
            $(document).ready(() => {
                // 数据抓取模块初始化已禁用，避免页面加载时影响功能
                // DataScraper.init();
                
                // 检查是否完成初次设置
                if (!CONFIG.FIRST_SETUP_DONE) {
                    // 首次使用，显示设置界面
                    this.showFirstTimeSetup();
                } else {
                    // 已设置，初始化界面
                    UI.init();

                    // 自动同步已禁用，避免页面加载时发起请求
                    // 如需同步，请手动点击界面按钮
                    console.log('🚫 [自动同步] 自动同步已禁用，避免影响页面功能');
                }

                Utils.log('INFO', '京麦数据抓取脚本初始化完成');
            });
        },

        // 显示首次设置界面
        showFirstTimeSetup: function() {
            const setupHtml = `
                <div id="first-setup-dialog" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 400px;
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                ">
                    <h3 style="margin: 0 0 15px 0; color: #333;">京麦数据同步 - 初次设置</h3>
                    <p style="color: #666; margin-bottom: 20px;">
                        欢迎使用京麦数据同步脚本！<br>
                        请设置管理员姓名并完成验证后，脚本将在您访问京麦页面时自动同步数据。
                    </p>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #888;">API服务器地址: (系统默认)</label>
                        <input id="setup-api-url" type="text" value="${CONFIG.API_BASE_URL}" readonly disabled style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                            background-color: #f5f5f5;
                            color: #888;
                            cursor: not-allowed;
                        ">
                        <small style="color: #999; margin-top: 2px; display: block;">
                            使用系统默认API地址，无需修改
                        </small>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #d32f2f;">管理员姓名: <span style="color: #d32f2f;">*</span></label>
                        <input id="setup-manager-name" type="text" value="${CONFIG.MANAGER_NAME}" placeholder="请输入管理员姓名（必填）" style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        ">
                        <small style="color: #666; margin-top: 5px; display: block;">
                            此姓名将用于匹配系统中的用户，作为VC账户的负责人
                        </small>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: flex; align-items: center;">
                            <input id="setup-auto-sync" type="checkbox" ${CONFIG.AUTO_SCRAPE ? 'checked' : ''} style="margin-right: 8px;">
                            <span>启用自动同步（推荐）</span>
                        </label>
                        <small style="color: #666; margin-left: 20px;">
                            开启后，访问京麦页面时会自动同步数据到系统
                        </small>
                    </div>

                    <div style="text-align: right;">
                        <button id="setup-test-manager" style="
                            padding: 8px 16px;
                            background: #17a2b8;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-right: 10px;
                        ">验证管理员</button>
                        <button id="setup-complete" style="
                            padding: 8px 16px;
                            background: #6c757d;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: not-allowed;
                            opacity: 0.6;
                        " disabled>完成设置</button>
                    </div>

                    <div id="setup-status" style="
                        margin-top: 15px;
                        padding: 8px;
                        border-radius: 4px;
                        display: none;
                    "></div>
                </div>
            `;

            $('body').append(setupHtml);

            // 绑定事件
            $('#setup-test-manager').click(() => this.testManagerMatch());
            $('#setup-complete').click(() => this.completeSetup());
            
            // 输入框变化时重新验证按钮状态
            $('#setup-manager-name').on('input', () => this.validateSetupForm());
            
            // 初始化时验证表单状态
            this.validateSetupForm();
        },

        // 验证设置表单状态
        validateSetupForm: function() {
            const managerName = $('#setup-manager-name').val().trim();
            const completeButton = $('#setup-complete');
            
            if (!managerName) {
                // 管理员姓名为空，禁用完成按钮
                completeButton.prop('disabled', true)
                    .css({
                        'background': '#6c757d',
                        'cursor': 'not-allowed',
                        'opacity': '0.6'
                    })
                    .text('完成设置');
                    
                // 清除之前的验证状态
                $('#setup-status').hide();
                GM_setValue('MANAGER_VERIFIED', false);
            } else {
                // 有输入但还未验证，显示为待验证状态
                const isVerified = GM_getValue('MANAGER_VERIFIED', false);
                const verifiedName = GM_getValue('VERIFIED_MANAGER_NAME', '');
                
                if (isVerified && verifiedName === managerName) {
                    // 已验证且姓名未改变
                    completeButton.prop('disabled', false)
                        .css({
                            'background': '#007cff',
                            'cursor': 'pointer',
                            'opacity': '1'
                        })
                        .text('完成设置');
                } else {
                    // 未验证或姓名已改变
                    completeButton.prop('disabled', true)
                        .css({
                            'background': '#ffc107',
                            'cursor': 'not-allowed',
                            'opacity': '0.8'
                        })
                        .text('请先验证管理员');
                        
                    // 如果姓名改变了，清除验证状态
                    if (verifiedName !== managerName) {
                        GM_setValue('MANAGER_VERIFIED', false);
                        $('#setup-status').hide();
                    }
                }
            }
        },

        // 测试连接
        testConnection: function() {
            $('#setup-status').show().html('正在测试连接...').css({
                'background': '#e3f2fd',
                'color': '#1976d2',
                'border': '1px solid #bbdefb'
            });

            // 测试API连接
            GM_xmlhttpRequest({
                method: 'GET',
                url: CONFIG.API_BASE_URL + '/test',
                timeout: 5000,
                onload: function(response) {
                    if (response.status === 200) {
                        $('#setup-status').html('✅ 连接成功！服务器响应正常').css({
                            'background': '#e8f5e8',
                            'color': '#2e7d32',
                            'border': '1px solid #c8e6c9'
                        });
                    } else {
                        $('#setup-status').html('❌ 连接失败：服务器响应异常').css({
                            'background': '#ffebee',
                            'color': '#c62828',
                            'border': '1px solid #ffcdd2'
                        });
                    }
                },
                onerror: function() {
                    $('#setup-status').html('❌ 连接失败：无法连接到服务器').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                },
                ontimeout: function() {
                    $('#setup-status').html('❌ 连接超时：请检查服务器地址').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                }
            });
        },

        // 测试管理员匹配
        testManagerMatch: function() {
            const managerName = $('#setup-manager-name').val().trim();
            if (!managerName) {
                $('#setup-status').show().html('❌ 请先输入管理员姓名').css({
                    'background': '#ffebee',
                    'color': '#c62828',
                    'border': '1px solid #ffcdd2'
                });
                return;
            }

            const apiUrl = $('#setup-api-url').val();
            $('#setup-status').show().html('正在验证管理员匹配...').css({
                'background': '#e3f2fd',
                'color': '#1976d2',
                'border': '1px solid #bbdefb'
            });

            // 测试管理员匹配
            GM_xmlhttpRequest({
                method: 'GET',
                url: CONFIG.API_BASE_URL + '/test-manager?managerName=' + encodeURIComponent(managerName),
                timeout: 5000,
                onload: function(response) {
                    if (response.status === 200) {
                        try {
                            const result = JSON.parse(response.responseText);
                            if (result.code === 0 && result.data) {
                                $('#setup-status').html(`✅ 管理员验证成功！用户ID: ${result.data.userId}, 姓名: ${result.data.nickname}`).css({
                                    'background': '#e8f5e8',
                                    'color': '#2e7d32',
                                    'border': '1px solid #c8e6c9'
                                });
                                
                                // 保存验证状态
                                GM_setValue('MANAGER_VERIFIED', true);
                                GM_setValue('VERIFIED_MANAGER_NAME', managerName);
                                
                                // 更新按钮状态
                                Main.validateSetupForm();
                            } else {
                                $('#setup-status').html('❌ 未找到匹配的管理员，请检查姓名是否正确').css({
                                    'background': '#fff3e0',
                                    'color': '#f57c00',
                                    'border': '1px solid #ffcc02'
                                });
                                
                                // 清除验证状态
                                GM_setValue('MANAGER_VERIFIED', false);
                                Main.validateSetupForm();
                            }
                        } catch (e) {
                            $('#setup-status').html('❌ 响应数据解析失败').css({
                                'background': '#ffebee',
                                'color': '#c62828',
                                'border': '1px solid #ffcdd2'
                            });
                            
                            GM_setValue('MANAGER_VERIFIED', false);
                            Main.validateSetupForm();
                        }
                    } else {
                        $('#setup-status').html('❌ 管理员验证失败：服务器响应异常').css({
                            'background': '#ffebee',
                            'color': '#c62828',
                            'border': '1px solid #ffcdd2'
                        });
                        
                        GM_setValue('MANAGER_VERIFIED', false);
                        Main.validateSetupForm();
                    }
                },
                onerror: function() {
                    $('#setup-status').html('❌ 管理员验证失败：无法连接到服务器').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                    
                    GM_setValue('MANAGER_VERIFIED', false);
                    Main.validateSetupForm();
                },
                ontimeout: function() {
                    $('#setup-status').html('❌ 管理员验证超时：请检查服务器地址').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                    
                    GM_setValue('MANAGER_VERIFIED', false);
                    Main.validateSetupForm();
                }
            });
        },

        // 完成设置
        completeSetup: function() {
            const managerName = $('#setup-manager-name').val().trim();
            const isVerified = GM_getValue('MANAGER_VERIFIED', false);
            const verifiedName = GM_getValue('VERIFIED_MANAGER_NAME', '');
            
            // 验证必填项和验证状态
            if (!managerName) {
                $('#setup-status').show().html('❌ 请输入管理员姓名').css({
                    'background': '#ffebee',
                    'color': '#c62828',
                    'border': '1px solid #ffcdd2'
                });
                return;
            }
            
            if (!isVerified || verifiedName !== managerName) {
                $('#setup-status').show().html('❌ 请先验证管理员姓名').css({
                    'background': '#fff3e0',
                    'color': '#f57c00',
                    'border': '1px solid #ffcc02'
                });
                return;
            }

            const autoSync = $('#setup-auto-sync').is(':checked');

            // 保存配置
            GM_setValue('AUTO_SCRAPE', autoSync);
            GM_setValue('MANAGER_NAME', managerName);
            GM_setValue('FIRST_SETUP_DONE', true);

            // 清除验证状态（已保存到配置中）
            GM_setValue('MANAGER_VERIFIED', false);
            GM_setValue('VERIFIED_MANAGER_NAME', '');

            // 更新当前配置
            CONFIG.AUTO_SCRAPE = autoSync;
            CONFIG.MANAGER_NAME = managerName;
            CONFIG.FIRST_SETUP_DONE = true;

            // 移除设置界面
            $('#first-setup-dialog').remove();

            // 显示成功消息
            GM_notification('设置完成', '京麦数据同步已配置完成，正在初始化...');

            // 立即初始化UI并开始自动同步，而不是刷新页面
            setTimeout(() => {
                UI.init();
                
                // 自动同步已禁用，避免设置完成后立即发起请求
                console.log('🚫 [自动同步] 设置完成后的自动同步已禁用');
            }, 500);
        },

        // 完成设置后的自动抓取（忽略已存在数据的检查）- 暂时屏蔽
        startAutoScrapeAfterSetup: function() {
            console.log('🚫 [测试模式] 首次设置后的自动抓取已屏蔽');
            GM_notification('测试模式', '首次设置完成，自动抓取已屏蔽，请手动测试', 'info');
            return; // 直接返回，不执行自动抓取

            Utils.log('INFO', '完成首次设置，启动自动抓取和上传');
            
            const pageType = Utils.getPageType();
            const existingQualifications = GM_getValue('scraped_qualifications', []);
            const existingAccount = GM_getValue('scraped_account', null);

            if (pageType === 'account') {
                if (existingAccount) {
                    // 如果已有账户数据，直接自动上传
                    Utils.log('INFO', '发现已存在的VC账户数据，直接上传');
                    GM_notification('自动同步', 'VC账户数据已存在，正在自动上传...', 'info');
                    setTimeout(async () => {
                        await UI.autoUploadAccount(existingAccount);
                    }, 1000);
                } else {
                    // 没有数据，开始抓取
                    GM_notification('自动同步', '检测到VC账户页面，5秒后开始自动同步...', 'info');
                    setTimeout(async () => {
                        await UI.handleScrapeAccount();
                    }, 5000);
                }

            } else if (pageType === 'qualification') {
                if (existingQualifications.length > 0) {
                    // 如果已有资质数据，直接自动上传
                    Utils.log('INFO', `发现已存在${existingQualifications.length}条资质数据，直接上传`);
                    GM_notification('自动同步', `发现${existingQualifications.length}条资质数据，正在自动上传...`, 'info');
                    setTimeout(async () => {
                        await UI.autoUploadAfterScrape(existingQualifications);
                    }, 1000);
                } else {
                    // 没有数据，开始抓取
                    GM_notification('自动同步', '检测到资质页面，8秒后开始自动同步...', 'info');
                    setTimeout(async () => {
                        await UI.handleScrapeQualifications();
                    }, 8000);
                }

            } else {
                // 未识别页面，检查是否有任何已抓取的数据需要上传
                if (existingAccount || existingQualifications.length > 0) {
                    Utils.log('INFO', '未识别页面类型，但发现已存在数据，尝试上传');
                    GM_notification('自动同步', '发现已抓取的数据，正在自动上传...', 'info');
                    
                    setTimeout(async () => {
                        try {
                            let uploadCount = 0;
                            
                            // 上传VC账户数据
                            if (existingAccount) {
                                await UI.autoUploadAccount(existingAccount);
                                uploadCount++;
                            }
                            
                            // 上传资质数据
                            if (existingQualifications.length > 0) {
                                await UI.autoUploadAfterScrape(existingQualifications);
                                uploadCount += existingQualifications.length;
                            }
                            
                            if (uploadCount > 0) {
                                Utils.log('INFO', `自动上传完成，共处理${uploadCount}条数据`);
                            }
                            
                        } catch (error) {
                            Utils.log('ERROR', '自动上传失败', error.message);
                            GM_notification('自动上传失败: ' + error.message, 'error');
                        }
                    }, 2000);
                } else {
                    Utils.log('INFO', '未识别页面类型且无已存在数据');
                    GM_notification('提示', '未识别的京麦页面类型，请手动导航到VC账户或资质管理页面', 'info');
                }
            }
        },

        // 重置到首次设置状态
        resetToFirstSetup: function() {
            // 显示更详细的确认对话框
            const confirmMessage = `确定要重置所有设置吗？

这将清除以下内容：
• API服务器地址
• 管理员姓名设置
• 自动同步开关
• 已抓取的数据

重置后页面将自动刷新，回到首次设置状态。`;

            if (confirm(confirmMessage)) {
                try {
                    // 清除所有配置，使用兼容的方式
                    if (typeof GM_deleteValue !== 'undefined') {
                        // 如果GM_deleteValue可用，使用它
                        GM_deleteValue('API_BASE_URL');
                        GM_deleteValue('AUTO_SCRAPE');
                        GM_deleteValue('MANAGER_NAME');
                        GM_deleteValue('FIRST_SETUP_DONE');
                        GM_deleteValue('scraped_account');
                        GM_deleteValue('scraped_qualifications');
                    } else {
                        // 如果GM_deleteValue不可用，设置为默认值
                        GM_setValue('API_BASE_URL', CONFIG.API_BASE_URL);
                        GM_setValue('AUTO_SCRAPE', true);
                        GM_setValue('MANAGER_NAME', '');
                        GM_setValue('FIRST_SETUP_DONE', false);
                        GM_setValue('scraped_account', null);
                        GM_setValue('scraped_qualifications', []);
                    }

                    // 更新当前配置
                    CONFIG.AUTO_SCRAPE = true;
                    CONFIG.MANAGER_NAME = '';
                    CONFIG.FIRST_SETUP_DONE = false;

                    // 显示提示
                    GM_notification('重置完成', '所有设置已清除，页面将自动刷新进入首次设置', 'success');

                    // 延迟刷新页面
                    setTimeout(() => {
                        location.reload();
                    }, 2000);

                } catch (error) {
                    console.error('重置设置时出错:', error);
                    GM_notification('重置失败', '重置过程中出现错误: ' + error.message, 'error');
                }
            }
        },

        // 启动自动抓取 - 暂时屏蔽，专注测试拦截功能
        startAutoScrape: function() {
            console.log('🚫 [测试模式] 自动抓取功能已暂时屏蔽，专注测试拦截功能');

            const pageType = Utils.getPageType();
            console.log('🔍 [页面检测] 当前页面类型:', pageType);

            // 只显示拦截状态，不执行自动抓取
            if (pageType === 'qualification') {
                console.log('🧪 [测试提示] 在资质页面，请手动操作测试拦截功能');
                GM_notification('测试模式', '检测到资质页面，请手动操作测试拦截功能', 'info');

                // 检查拦截状态
                setTimeout(() => {
                    const interceptStatus = DataScraper.checkInterceptStatus();
                    console.log('🔍 [拦截检查] 资质页面拦截状态:', interceptStatus);
                }, 2000);
            } else if (pageType === 'account') {
                console.log('🧪 [测试提示] 在VC账户页面，可以测试账户数据抓取');
                GM_notification('测试模式', '检测到VC账户页面，可以手动测试账户抓取', 'info');
            }

            return; // 直接返回，不执行原有的自动抓取逻辑

            const pageType_disabled = Utils.getPageType();
            
            // 检查是否已有数据，避免重复抓取
            const existingQualifications = GM_getValue('scraped_qualifications', []);
            const existingAccount = GM_getValue('scraped_account', null);

            if (pageType === 'account') {
                if (existingAccount) {
                    // 如果已有账户数据，检查是否需要重新上传
                    const syncedVcInfo = ApiClient.getSyncedVcAccountInfo();
                    const lastAccountUploadTime = GM_getValue('last_account_upload_time', '');
                    const today = new Date().toDateString();
                    
                    // 检查是否今天已经上传过VC账户
                    const isAccountUploadedToday = lastAccountUploadTime === today;
                    const hasValidSync = syncedVcInfo && syncedVcInfo.vcAccountId;
                    
                    if (isAccountUploadedToday && hasValidSync) {
                        Utils.log('INFO', 'VC账户数据已存在且今日已上传，跳过自动处理');
                        GM_notification('提示', 'VC账户数据今日已同步，无需重复处理', 'info');
                    } else {
                        // 数据存在但今日未上传，自动上传
                        const reason = !isAccountUploadedToday ? '今日尚未上传' : '同步信息缺失';
                        Utils.log('INFO', `VC账户数据已存在但${reason}，开始自动上传`);
                        GM_notification('自动同步', 'VC账户数据已存在，正在自动上传...', 'info');
                        setTimeout(async () => {
                            await UI.autoUploadAccount(existingAccount);
                        }, 3000);
                    }
                    return;
                }
                
                // 在账户页面自动抓取账户信息
                GM_notification('自动同步', '检测到VC账户页面，10秒后开始自动同步...', 'info');
                setTimeout(async () => {
                    await UI.handleScrapeAccount();
                }, 10000); // 延长到10秒，避免频繁请求

            } else if (pageType === 'qualification') {
                if (existingQualifications.length > 0) {
                    // 如果已有资质数据，检查是否需要重新上传
                    const uploadSuccessCount = GM_getValue('upload_success_count', 0);
                    const lastUploadTime = GM_getValue('last_upload_time', '');
                    const today = new Date().toDateString(); // 获取今天的日期字符串
                    
                    // 检查是否今天已经上传过，且上传数量匹配
                    const isUploadedToday = lastUploadTime === today;
                    const isFullyUploaded = uploadSuccessCount > 0 && uploadSuccessCount >= existingQualifications.length;
                    
                    if (isUploadedToday && isFullyUploaded) {
                        Utils.log('INFO', `资质数据已存在${existingQualifications.length}条且今日已上传，跳过自动处理`);
                        GM_notification('提示', `资质数据今日已同步，共${existingQualifications.length}条记录`, 'info');
                    } else {
                        // 数据存在但今日未上传或未完全上传，自动上传
                        const reason = !isUploadedToday ? '今日尚未上传' : '未完全上传';
                        Utils.log('INFO', `发现${existingQualifications.length}条资质数据${reason}，开始自动上传`);
                        GM_notification('自动同步', `发现${existingQualifications.length}条资质数据，正在自动上传...`, 'info');
                        setTimeout(async () => {
                            await UI.autoUploadAfterScrape(existingQualifications);
                        }, 3000);
                    }
                    return;
                }
                
                // 在资质页面自动抓取资质数据
                GM_notification('自动同步', '检测到资质页面，15秒后开始自动同步...', 'info');
                setTimeout(async () => {
                    await UI.handleScrapeQualifications();
                }, 15000); // 进一步延长到15秒，给页面充分加载时间
                
            } else {
                Utils.log('INFO', '未识别的页面类型，跳过自动同步');
                GM_notification('提示', '未识别的京麦页面类型', 'info');
            }
        }
    };
    
    // 启动应用
    Main.init();

    // 暴露重置方法到全局，方便控制台调用
    window.resetJingmaiScript = function() {
        Main.resetToFirstSetup();
    };

    // 暴露设置方法到全局，方便控制台调用
    window.showJingmaiSettings = function() {
        UI.showSettingsDialog();
    };

    // ===== 简单有效的全局拦截器（立即执行，直接复制成功拦截器的逻辑）=====
    let interceptedApiData = []; // 存储拦截到的数据
    let totalDataCount = 0; // 总数据量
    let currentDataCount = 0; // 已抓取数量
    const TARGET_API_SIMPLE = 'https://sff.jd.com/api?v=1.0&appId=XRICZR1JYFQENYERSMUP&api=dsm.omni.qua.product.BrandRelationReadService.queryBrandRelationFormalList';

    // 立即设置拦截器，不等待手动调用
    (function setupSimpleIntercept() {
        console.log('🎯 设置简单拦截器，目标API:', TARGET_API_SIMPLE);

        // 拦截fetch请求 - 直接复制成功拦截器的代码
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const [resource, config = {}] = args;
            
            // 检查是否是目标API
            if (resource === TARGET_API_SIMPLE || (typeof resource === 'object' && resource.url === TARGET_API_SIMPLE)) {
                console.log('🎯 检测到目标API请求:', resource);
                
                // 调用原始fetch并拦截响应
                return originalFetch.apply(this, args)
                    .then(response => {
                        // 克隆响应以便读取数据而不影响原始请求
                        const responseClone = response.clone();
                        
                        // 读取并处理响应数据
                        responseClone.json()
                            .then(data => {
                                console.group('🎉 成功拦截到资质API响应');
                                console.log('📊 响应状态:', response.status, response.statusText);
                                console.log('📋 完整响应数据:', data);
                                
                                if (data.data && data.data.itemList) {
                                    console.log('📝 资质列表数据:');
                                    console.table(data.data.itemList.slice(0, 5));
                                    console.log('📄 分页信息:', data.data.pageInfoFacet);
                                    console.log('🔢 总计:', data.data.pageInfoFacet?.totalCount, '个品牌关系记录');
                                    console.log('📝 本页数据:', data.data.itemList.length, '条');
                                    
                                    // 更新进度统计
                                    if (data.data.pageInfoFacet?.totalCount) {
                                        totalDataCount = data.data.pageInfoFacet.totalCount;
                                    }
                                    
                                    // 合并新数据，避免重复（使用brandRelationId作为唯一标识）
                                    const newItems = data.data.itemList || [];
                                    const existingIds = new Set(interceptedApiData.flatMap(d => (d.data?.itemList || []).map(item => 
                                        item.brandRelationId || item.brandQuaId || item.id || item.brandId
                                    )));
                                    const uniqueNewItems = newItems.filter(item => !existingIds.has(
                                        item.brandRelationId || item.brandQuaId || item.id || item.brandId
                                    ));
                                    
                                    console.log(`🔍 [去重检查] 新数据${newItems.length}条，已存在ID数量${existingIds.size}，过滤后唯一数据${uniqueNewItems.length}条`);
                                    
                                    if (uniqueNewItems.length > 0) {
                                        // 创建新的数据对象，只包含唯一项
                                        const uniqueData = {
                                            ...data,
                                            data: {
                                                ...data.data,
                                                itemList: uniqueNewItems
                                            }
                                        };
                                        interceptedApiData.push(uniqueData);
                                        currentDataCount += uniqueNewItems.length;
                                        
                                        console.log(`✨ 新增 ${uniqueNewItems.length} 条唯一数据，当前总计: ${currentDataCount}/${totalDataCount}`);
                                    } else {
                                        console.log('ℹ️ 本页数据已存在，跳过重复数据');
                                    }
                                    
                                    // 保存到全局数组和缓存
                                    GM_setValue('cached_real_response', {
                                        timestamp: Date.now(),
                                        response: data,
                                        requestData: { url: resource, timestamp: Date.now() }
                                    });
                                    
                                    // 更新进度显示
                                    updateProgressDisplay();
                                    
                                    // 如果有京麦脚本的cacheRealResponse方法，也调用它
                                    if (DataScraper && typeof DataScraper.cacheRealResponse === 'function') {
                                        DataScraper.cacheRealResponse(data);
                                    }
                                }
                                console.groupEnd();
                            })
                            .catch(err => {
                                console.error('❌ 解析响应数据失败:', err);
                            });
                        
                        // 返回原始响应给页面使用
                        return response;
                    })
                    .catch(err => {
                        console.error('❌ API请求失败:', err);
                        throw err;
                    });
            }
            
            // 非目标API，直接通过
            return originalFetch.apply(this, args);
        };

        // 拦截XMLHttpRequest请求（备用方案）
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._method = method;
            this._url = url;
            return originalXHROpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            if (this._url === TARGET_API_SIMPLE) {
                console.log('🎯 检测到目标API请求(XMLHttpRequest):', this._url);
                
                // 监听响应（不修改请求）
                this.addEventListener('load', function() {
                    if (this.status === 200) {
                        try {
                            const responseData = JSON.parse(this.responseText);
                            console.group('🎉 成功拦截到资质API响应(XMLHttpRequest)');
                            console.log('📊 响应状态:', this.status, this.statusText);
                            console.log('📋 完整响应数据:', responseData);
                            
                            if (responseData.data && responseData.data.itemList) {
                                console.log('📝 资质列表数据:');
                                console.table(responseData.data.itemList.slice(0, 5));
                                console.log('📄 分页信息:', responseData.data.pageInfoFacet);
                                console.log('🔢 总计:', responseData.data.pageInfoFacet?.totalCount, '个品牌关系记录');
                                
                                // 更新进度统计
                                if (responseData.data.pageInfoFacet?.totalCount) {
                                    totalDataCount = responseData.data.pageInfoFacet.totalCount;
                                }
                                
                                // 合并新数据，避免重复（使用brandRelationId作为唯一标识）
                                const newItems = responseData.data.itemList || [];
                                const existingIds = new Set(interceptedApiData.flatMap(d => (d.data?.itemList || []).map(item => 
                                    item.brandRelationId || item.brandQuaId || item.id || item.brandId
                                )));
                                const uniqueNewItems = newItems.filter(item => !existingIds.has(
                                    item.brandRelationId || item.brandQuaId || item.id || item.brandId
                                ));
                                
                                console.log(`🔍 [去重检查] 新数据${newItems.length}条，已存在ID数量${existingIds.size}，过滤后唯一数据${uniqueNewItems.length}条`);
                                
                                if (uniqueNewItems.length > 0) {
                                    const uniqueData = {
                                        ...responseData,
                                        data: {
                                            ...responseData.data,
                                            itemList: uniqueNewItems
                                        }
                                    };
                                    interceptedApiData.push(uniqueData);
                                    currentDataCount += uniqueNewItems.length;
                                    
                                    console.log(`✨ 新增 ${uniqueNewItems.length} 条唯一数据，当前总计: ${currentDataCount}/${totalDataCount}`);
                                } else {
                                    console.log('ℹ️ 本页数据已存在，跳过重复数据');
                                }
                                
                                // 保存到全局数组和缓存
                                GM_setValue('cached_real_response', {
                                    timestamp: Date.now(),
                                    response: responseData,
                                    requestData: { url: this._url, timestamp: Date.now() }
                                });
                                
                                // 更新进度显示
                                updateProgressDisplay();
                                
                                // 如果有京麦脚本的cacheRealResponse方法，也调用它
                                if (DataScraper && typeof DataScraper.cacheRealResponse === 'function') {
                                    DataScraper.cacheRealResponse(responseData);
                                }
                            }
                            console.groupEnd();
                        } catch (err) {
                            console.error('❌ 解析XMLHttpRequest响应数据失败:', err);
                        }
                    }
                });
            }
            
            return originalXHRSend.apply(this, arguments);
        };

        console.log('✅ 简单拦截器设置完成，等待API调用...');
    })(); // 立即执行拦截器设置

    // ===== 进度显示和上传功能 =====
    
    // 创建进度显示面板
    function createProgressPanel() {
        const panelHtml = `
            <div id="jingmai-progress-panel" style="
                position: fixed;
                top: 20px;
                right: 20px;
                width: 300px;
                background: white;
                border: 2px solid #007bff;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 12px;
            ">
                <div style="
                    background: #007bff;
                    color: white;
                    margin: -15px -15px 10px -15px;
                    padding: 10px 15px;
                    border-radius: 6px 6px 0 0;
                    font-weight: bold;
                    cursor: move;
                ">📊 数据抓取进度</div>
                
                <div style="margin-bottom: 10px;">
                    <div><strong>抓取进度:</strong></div>
                    <div style="
                        background: #f0f0f0;
                        border-radius: 10px;
                        height: 20px;
                        margin: 5px 0;
                        position: relative;
                        overflow: hidden;
                    ">
                        <div id="progress-bar" style="
                            background: linear-gradient(90deg, #28a745, #20c997);
                            height: 100%;
                            width: 0%;
                            transition: width 0.3s ease;
                            border-radius: 10px;
                        "></div>
                        <div id="progress-text" style="
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 10px;
                            font-weight: bold;
                            color: #333;
                        ">0% (0/0)</div>
                    </div>
                </div>
                
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <div><strong>已抓取:</strong> <span id="current-count">0</span></div>
                    <div><strong>总计:</strong> <span id="total-count">0</span></div>
                </div>
                
                <div style="display: flex; gap: 8px;">
                    <button id="upload-btn" style="
                        flex: 1;
                        padding: 8px 12px;
                        background: #28a745;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 11px;
                        font-weight: bold;
                    ">上传数据</button>
                    
                    <button id="clear-btn" style="
                        padding: 8px 12px;
                        background: #dc3545;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 11px;
                    ">清空</button>
                    
                    <button id="hide-panel-btn" style="
                        padding: 8px 12px;
                        background: #6c757d;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 11px;
                    ">隐藏</button>
                </div>
                
                <div id="status-text" style="
                    margin-top: 10px;
                    padding: 5px;
                    background: #f8f9fa;
                    border-radius: 4px;
                    text-align: center;
                    font-size: 10px;
                    color: #666;
                ">等待数据拦截...</div>
            </div>
        `;

        // 移除已存在的面板
        const existingPanel = document.getElementById('jingmai-progress-panel');
        if (existingPanel) {
            existingPanel.remove();
        }
        
        // 添加新面板
        document.body.insertAdjacentHTML('beforeend', panelHtml);
        
        // 绑定事件
        bindProgressPanelEvents();
    }

    // 绑定进度面板事件
    function bindProgressPanelEvents() {
        // 上传按钮 - 添加状态检查
        document.getElementById('upload-btn').addEventListener('click', function() {
            const btn = this;
            if (btn.disabled) {
                const message = currentDataCount > 0 
                    ? '请等待数据抓取完成后再上传' 
                    : '请先进行数据抓取';
                GM_notification(message, 'info');
                console.log(`ℹ️ ${message}`);
                return;
            }
            uploadInterceptedData();
        });
        
        // 清空按钮
        document.getElementById('clear-btn').addEventListener('click', clearInterceptedData);
        
        // 隐藏按钮
        document.getElementById('hide-panel-btn').addEventListener('click', function() {
            const panel = document.getElementById('jingmai-progress-panel');
            if (panel) {
                panel.style.display = 'none';
                console.log('📊 [进度面板] 面板已隐藏，使用 showProgressPanel() 重新显示');
            }
        });
        
        // 拖拽功能
        makeProgressPanelDraggable();
        
        // 初始化按钮状态
        const uploadBtn = document.getElementById('upload-btn');
        if (uploadBtn) {
            uploadBtn.disabled = true;
            uploadBtn.style.opacity = '0.6';
            uploadBtn.style.cursor = 'not-allowed';
            uploadBtn.title = '请先抓取数据';
        }
    }

    // 更新进度显示
    function updateProgressDisplay() {
        let panel = document.getElementById('jingmai-progress-panel');
        
        // 如果有数据但面板不存在，自动创建并显示
        if (!panel && (currentDataCount > 0 || totalDataCount > 0)) {
            createProgressPanel();
            panel = document.getElementById('jingmai-progress-panel');
            console.log('📊 [进度面板] 检测到数据拦截，自动显示进度面板');
        }
        
        if (!panel) return; // 如果面板仍不存在，直接返回
        
        const percent = totalDataCount > 0 ? Math.round((currentDataCount / totalDataCount) * 100) : 0;
        
        // 安全地更新DOM元素
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        const currentCountEl = document.getElementById('current-count');
        const totalCountEl = document.getElementById('total-count');
        const statusText = document.getElementById('status-text');
        const uploadBtn = document.getElementById('upload-btn');
        
        if (progressBar) progressBar.style.width = percent + '%';
        if (progressText) progressText.textContent = `${percent}% (${currentDataCount}/${totalDataCount})`;
        if (currentCountEl) currentCountEl.textContent = currentDataCount;
        if (totalCountEl) totalCountEl.textContent = totalDataCount;
        
        // 更新状态文本和按钮状态
        if (statusText) {
            if (currentDataCount === 0) {
                statusText.textContent = '等待数据拦截...';
                statusText.style.color = '#666';
                statusText.style.fontWeight = 'normal';
            } else if (currentDataCount >= totalDataCount && totalDataCount > 0) {
                statusText.textContent = '✅ 数据抓取完成！可以上传了';
                statusText.style.color = '#28a745';
                statusText.style.fontWeight = 'bold';
            } else {
                statusText.textContent = `⏳ 正在抓取中... (${currentDataCount}/${totalDataCount})`;
                statusText.style.color = '#007bff';
                statusText.style.fontWeight = 'normal';
            }
        }
        
        // 控制上传按钮状态：只有数据抓取完成后才能上传
        if (uploadBtn) {
            const isComplete = currentDataCount >= totalDataCount && totalDataCount > 0 && currentDataCount > 0;
            if (isComplete) {
                uploadBtn.disabled = false;
                uploadBtn.style.opacity = '1';
                uploadBtn.style.cursor = 'pointer';
                uploadBtn.title = '数据抓取完成，可以上传';
            } else {
                uploadBtn.disabled = true;
                uploadBtn.style.opacity = '0.6';
                uploadBtn.style.cursor = 'not-allowed';
                uploadBtn.title = currentDataCount > 0 ? '请等待数据抓取完成' : '请先抓取数据';
            }
        }
    }

    // 拖拽功能
    function makeProgressPanelDraggable() {
        const panel = document.getElementById('jingmai-progress-panel');
        const header = panel.querySelector('div');
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        
        header.addEventListener('mousedown', function(e) {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            const rect = panel.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;
            
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        });
        
        function handleMouseMove(e) {
            if (isDragging) {
                const newLeft = startLeft + (e.clientX - startX);
                const newTop = startTop + (e.clientY - startY);
                panel.style.left = newLeft + 'px';
                panel.style.top = newTop + 'px';
                panel.style.right = 'auto';
            }
        }
        
        function handleMouseUp() {
            isDragging = false;
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        }
    }

    // 上传拦截的数据
    async function uploadInterceptedData() {
        if (interceptedApiData.length === 0) {
            GM_notification('没有可上传的数据，请先进行数据抓取', 'warning');
            console.log('❌ 没有可上传的数据');
            return;
        }

        // 检查数据是否抓取完成
        const isComplete = currentDataCount >= totalDataCount && totalDataCount > 0 && currentDataCount > 0;
        if (!isComplete) {
            const message = totalDataCount > 0 
                ? `数据抓取未完成 (${currentDataCount}/${totalDataCount})，建议等待完成后上传` 
                : '总数据量未知，建议继续抓取更多数据后上传';
            
            if (!confirm(`${message}\n\n是否确认现在上传已抓取的 ${currentDataCount} 条数据？`)) {
                console.log('❌ 用户取消上传');
                return;
            }
        }

        const uploadBtn = document.getElementById('upload-btn');
        const statusText = document.getElementById('status-text');
        
        uploadBtn.disabled = true;
        uploadBtn.textContent = '上传中...';
        statusText.textContent = '📤 正在上传数据...';
        statusText.style.color = '#007bff';

        try {
            // 合并所有拦截的数据
            const allQualifications = [];
            for (const apiResponse of interceptedApiData) {
                if (apiResponse.data && apiResponse.data.itemList) {
                    for (const item of apiResponse.data.itemList) {
                        // 转换数据格式（与原有脚本完全一致）
                        const extendedInfo = {
                            brandGrade: item.brandGrade || 0,
                            brandGradeName: item.brandGradeName || '',
                            brandType: item.brandType || 0,
                            brandTypeDesc: item.brandTypeDesc || '',
                            statusDesc: item.statusDesc || '',
                            brandRelationId: item.brandRelationId || '',
                            brandQuaId: item.brandQuaId || '',
                            isChange: item.isChange || -1,
                            scrapedAt: new Date().toISOString(),
                            rawData: item
                        };

                        const transformedItem = {
                            // 使用与原有脚本相同的字段映射
                            vcAccount: item.vendorCode || '',
                            brand: item.brandName || '',
                            brandCode: item.brandCode || '',
                            productLineLevel: item.brandGradeName || '',
                            firstCategory: item.brandSort1Name || '',
                            firstCategoryCode: item.brandSort1Code || '',
                            secondCategory: item.brandSort2Name || '',
                            secondCategoryCode: item.brandSort2Code || '',
                            thirdCategory: item.brandSort3Name || '',
                            thirdCategoryCode: item.brandSort3Code || '',
                            qualificationExpireDate: item.expiryDate ? Utils.formatDate(item.expiryDate) : '',
                            purchaser: item.purchaserCode || '',
                            purchaserName: item.purchaserName || '',
                            firstDepartment: item.deptName || '',
                            firstDepartmentCode: item.deptCode || '',
                            productLineType: item.brandTypeDesc || '',
                            status: item.status || 0,  // 使用数字类型，不是字符串
                            scraped_at: new Date().toISOString(),
                            remark: `京麦同步数据: ${JSON.stringify(extendedInfo)}`
                        };
                        allQualifications.push(transformedItem);
                    }
                }
            }

            console.log(`准备上传 ${allQualifications.length} 条资质数据`);

            // 使用原有脚本的ApiClient上传方法
            if (ApiClient && typeof ApiClient.batchUploadQualifications === 'function') {
                const successCount = await ApiClient.batchUploadQualifications(allQualifications);
                
                statusText.textContent = `✅ 上传完成！成功 ${successCount} 条`;
                statusText.style.color = '#28a745';
                statusText.style.fontWeight = 'bold';
                
                GM_notification(`数据上传成功，共 ${successCount} 条`, 'success');
                
                // 保存上传成功的统计
                GM_setValue('upload_success_count', successCount);
                GM_setValue('last_upload_time', new Date().toDateString());
                
                console.log(`✅ 数据上传成功，共 ${successCount} 条`);
            } else {
                throw new Error('未找到上传API方法，请确保原有脚本的ApiClient可用');
            }

        } catch (error) {
            console.error('❌ 数据上传失败:', error);
            statusText.textContent = `❌ 上传失败: ${error.message}`;
            statusText.style.color = '#dc3545';
            GM_notification(`数据上传失败: ${error.message}`, 'error');
        } finally {
            uploadBtn.disabled = false;
            uploadBtn.textContent = '上传数据';
        }
    }

    // 清空拦截的数据
    function clearInterceptedData() {
        if (confirm('确定要清空所有已抓取的数据吗？')) {
            interceptedApiData = [];
            currentDataCount = 0;
            totalDataCount = 0;
            
            GM_setValue('cached_real_response', null);
            
            updateProgressDisplay();
            
            console.log('🗑️ 已清空所有拦截数据');
            GM_notification('已清空所有拦截数据', 'info');
        }
    }

    // 显示进度面板
    function showProgressPanel() {
        let panel = document.getElementById('jingmai-progress-panel');
        if (panel) {
            panel.style.display = 'block';
            console.log('📊 [进度面板] 面板已显示');
        } else {
            createProgressPanel();
            panel = document.getElementById('jingmai-progress-panel');
            console.log('📊 [进度面板] 面板已创建并显示');
        }
        
        // 确保显示后立即更新数据
        if (panel) {
            updateProgressDisplay();
        }
    }

    // 不再自动创建进度面板，改为按需创建
    // 页面加载后只在控制台提示如何显示面板
    console.log('💡 [进度面板] 使用 showProgressPanel() 命令显示数据抓取进度面板');

    // 暴露初始化DataScraper属性的功能（拦截器已自动启动）
    window.startJingmaiIntercept = function() {
        console.log('ℹ️ [说明] 全局拦截器已在脚本加载时自动启动');
        
        // 初始化DataScraper的属性（如果需要的话）
        if (DataScraper) {
            DataScraper.cachedRealRequestData = null;
            DataScraper.interceptRetryCount = 0;
            DataScraper.maxInterceptRetries = 5;
            DataScraper.lastReinitTime = 0;
            console.log('✅ DataScraper属性已初始化');
        }
        
        console.log('💡 [提示] 全局拦截器已经在工作，请在页面上进行操作（如翻页、筛选）来触发API请求');
        console.log('📝 [检查] 使用 checkInterceptedData() 命令查看拦截结果');
        console.log('📊 [面板] 使用 showProgressPanel() 命令显示抓取进度面板');
    };

    // 暴露查看拦截数据的方法
    window.checkInterceptedData = function() {
        console.log('📊 拦截到的数据:', interceptedApiData);
        console.log(`📈 进度统计: ${currentDataCount}/${totalDataCount} (${totalDataCount > 0 ? Math.round(currentDataCount/totalDataCount*100) : 0}%)`);
        
        // 统计唯一数据
        const allItems = interceptedApiData.flatMap(d => d.data?.itemList || []);
        const uniqueItems = Array.from(new Map(allItems.map(item => [
            item.brandRelationId || item.brandQuaId || item.id || item.brandId, 
            item
        ])).values());
        console.log(`🔢 数据统计: 总响应${interceptedApiData.length}个，总项目${allItems.length}个，唯一项目${uniqueItems.length}个`);
        
        const cachedData = GM_getValue('cached_real_response', null);
        console.log('💾 缓存的数据:', cachedData);
        
        if (uniqueItems.length > 0) {
            console.log('📋 数据预览:');
            console.table(uniqueItems.slice(0, 5));
        }
        
        return { 
            interceptedApiData, 
            cachedData, 
            stats: {
                responses: interceptedApiData.length,
                totalItems: allItems.length,
                uniqueItems: uniqueItems.length,
                progress: `${currentDataCount}/${totalDataCount}`,
                percentage: totalDataCount > 0 ? Math.round(currentDataCount/totalDataCount*100) : 0
            }
        };
    };

    // 暴露进度面板控制方法
    window.showProgressPanel = showProgressPanel;
    
    window.hideProgressPanel = function() {
        const panel = document.getElementById('jingmai-progress-panel');
        if (panel) {
            panel.style.display = 'none';
            console.log('📊 [进度面板] 面板已隐藏，使用 showProgressPanel() 重新显示');
        } else {
            console.log('📊 [进度面板] 面板不存在');
        }
    };
    
    window.uploadNow = uploadInterceptedData;
    
    window.clearData = clearInterceptedData;
    
    // 查看转换后的上传数据格式（调试用）
    window.previewUploadData = function() {
        if (interceptedApiData.length === 0) {
            console.log('❌ 没有可预览的数据');
            return { error: '没有可预览的数据' };
        }

        console.group('🔍 [数据预览] 转换后的上传数据格式');
        
        // 合并所有拦截的数据并转换格式
        const allQualifications = [];
        for (const apiResponse of interceptedApiData) {
            if (apiResponse.data && apiResponse.data.itemList) {
                for (const item of apiResponse.data.itemList) {
                    // 使用相同的转换逻辑
                    const extendedInfo = {
                        brandGrade: item.brandGrade || 0,
                        brandGradeName: item.brandGradeName || '',
                        brandType: item.brandType || 0,
                        brandTypeDesc: item.brandTypeDesc || '',
                        statusDesc: item.statusDesc || '',
                        brandRelationId: item.brandRelationId || '',
                        brandQuaId: item.brandQuaId || '',
                        isChange: item.isChange || -1,
                        scrapedAt: new Date().toISOString(),
                        rawData: item
                    };

                    const transformedItem = {
                        vcAccount: item.vendorCode || '',
                        brand: item.brandName || '',
                        brandCode: item.brandCode || '',
                        productLineLevel: item.brandGradeName || '',
                        firstCategory: item.brandSort1Name || '',
                        firstCategoryCode: item.brandSort1Code || '',
                        secondCategory: item.brandSort2Name || '',
                        secondCategoryCode: item.brandSort2Code || '',
                        thirdCategory: item.brandSort3Name || '',
                        thirdCategoryCode: item.brandSort3Code || '',
                        qualificationExpireDate: item.expiryDate ? Utils.formatDate(item.expiryDate) : '',
                        purchaser: item.purchaserCode || '',
                        purchaserName: item.purchaserName || '',
                        firstDepartment: item.deptName || '',
                        firstDepartmentCode: item.deptCode || '',
                        productLineType: item.brandTypeDesc || '',
                        status: item.status || 0,
                        scraped_at: new Date().toISOString(),
                        remark: `京麦同步数据: ${JSON.stringify(extendedInfo)}`
                    };
                    allQualifications.push(transformedItem);
                }
            }
        }

        console.log(`📊 总计转换数据: ${allQualifications.length} 条`);
        console.log('📋 前5条数据预览:');
        console.table(allQualifications.slice(0, 5));
        
        console.log('📋 上传请求体格式:');
        const uploadPayload = {
            qualifications: allQualifications,
            syncMode: 2,
            dataSource: 'TAMPERMONKEY_SCRIPT'
        };
        console.log(uploadPayload);
        
        console.log(`🔗 上传接口: ${CONFIG.API_BASE_URL}/qualifications/batch`);
        console.groupEnd();
        
        return {
            totalCount: allQualifications.length,
            sampleData: allQualifications.slice(0, 5),
            uploadPayload,
            apiUrl: `${CONFIG.API_BASE_URL}/qualifications/batch`
        };
    };
    
    // 强制重新开始抓取（清空所有数据并重置状态）
    window.resetAndRestart = function() {
        if (confirm('确定要清空所有数据并重新开始抓取吗？这将清除已拦截的所有数据。')) {
            // 清空所有全局变量
            interceptedApiData = [];
            currentDataCount = 0;
            totalDataCount = 0;
            
            // 清空存储
            GM_setValue('cached_real_response', null);
            
            // 更新显示
            updateProgressDisplay();
            
            console.log('🔄 已重置所有数据，请重新在页面上操作来开始抓取');
            GM_notification('已重置，可以重新开始抓取', 'info');
            
            // 显示进度面板
            showProgressPanel();
        }
    };

    // 暴露调试方法到全局
    window.checkJingmaiData = function() {
        try {
            const accountData = GM_getValue('scraped_account', null);
            const qualifications = GM_getValue('scraped_qualifications', []);
            const syncedVcInfo = GM_getValue('vc_account_sync_result', null);
            const uploadSuccessCount = GM_getValue('upload_success_count', 0);
            const lastUploadTime = GM_getValue('last_upload_time', '');
            const lastAccountUploadTime = GM_getValue('last_account_upload_time', '');
            const today = new Date().toDateString();
            
            console.log('存储数据检查:');
            console.log('- VC账户数据:', accountData);
            console.log('- 资质数据数量:', Array.isArray(qualifications) ? qualifications.length : 0);
            console.log('- 已同步VC账户信息:', syncedVcInfo);
            console.log('- 上传成功数量:', uploadSuccessCount);
            console.log('- 最后上传时间:', lastUploadTime);
            console.log('- 最后VC账户上传时间:', lastAccountUploadTime);
            console.log('- 今天日期:', today);
            console.log('- 资质是否今日已上传:', lastUploadTime === today);
            console.log('- VC账户是否今日已上传:', lastAccountUploadTime === today);
            
            return {
                accountData,
                qualifications,
                syncedVcInfo,
                uploadSuccessCount,
                lastUploadTime,
                lastAccountUploadTime,
                today,
                isQualificationUploadedToday: lastUploadTime === today,
                isAccountUploadedToday: lastAccountUploadTime === today
            };
        } catch (error) {
            console.error('检查数据失败:', error);
            return null;
        }
    };

    window.checkJingmaiIntercept = function() {
        try {
            if (DataScraper && typeof DataScraper.checkInterceptStatus === 'function') {
                return DataScraper.checkInterceptStatus();
            } else {
                console.error('DataScraper模块未初始化');
                return null;
            }
        } catch (error) {
            console.error('检查拦截状态失败:', error);
            return null;
        }
    };

    window.clearJingmaiData = function() {
        try {
            GM_setValue('scraped_account', null);
            GM_setValue('scraped_qualifications', []);
            GM_setValue('vc_account_sync_result', null);
            GM_setValue('upload_success_count', 0);
            GM_setValue('last_upload_time', '');
            GM_setValue('last_account_upload_time', '');
            
            console.log('已清理所有抓取数据和上传记录，可以重新开始抓取');
            
            // 更新UI统计
            if (typeof UI !== 'undefined' && UI.updateStatistics) {
                UI.updateStatistics();
            }
            
            return '数据清理完成';
        } catch (error) {
            console.error('清理数据失败:', error);
            return '清理失败: ' + error.message;
        }
    };

    window.reinitJingmaiIntercept = function() {
        try {
            if (DataScraper && typeof DataScraper.reinitializeIntercept === 'function') {
                console.log('🔄 手动重新初始化拦截机制');
                DataScraper.reinitializeIntercept();
                return '拦截机制已重新初始化';
            } else {
                console.error('DataScraper模块未初始化');
                return '初始化失败';
            }
        } catch (error) {
            console.error('重新初始化拦截失败:', error);
            return '初始化失败: ' + error.message;
        }
    };

    // 暴露强制重置方法（用于紧急情况）
    window.forceResetJingmaiScript = function() {
        try {
            // 强制重置所有配置为默认值
            GM_setValue('API_BASE_URL', CONFIG.API_BASE_URL);
            GM_setValue('AUTO_SCRAPE', true);
            GM_setValue('MANAGER_NAME', '');
            GM_setValue('FIRST_SETUP_DONE', false);
            GM_setValue('scraped_account', null);
            GM_setValue('scraped_qualifications', []);

            console.log('强制重置完成，即将刷新页面...');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } catch (error) {
            console.error('强制重置失败:', error);
        }
    };

    // 设备限制检测和解决方案工具
    window.deviceBlockSolution = function() {
        console.group('🚫 [设备限制] 解决方案指南');
        
        // 检测当前限制状态
        console.log('📊 当前状态检测:');
        console.log('- 设备指纹:', navigator.userAgent.substring(0, 50) + '...');
        console.log('- 屏幕分辨率:', `${screen.width}x${screen.height}`);
        console.log('- 时区:', Intl.DateTimeFormat().resolvedOptions().timeZone);
        console.log('- 语言:', navigator.language);
        console.log('- 硬件并发:', navigator.hardwareConcurrency);
        
        console.log('\n🔧 解决方案（按优先级排序）:');
        console.log('1. 🌐 网络切换方案:');
        console.log('   - 使用手机热点切换网络');
        console.log('   - 使用VPN更改IP地址');
        console.log('   - 切换到其他网络环境（如公司网络）');
        
        console.log('\n2. 🖥️ 设备环境方案:');
        console.log('   - 使用无痕/隐私模式浏览器');
        console.log('   - 更换不同的浏览器（Edge/Firefox/Chrome）');
        console.log('   - 使用虚拟机或其他设备');
        
        console.log('\n3. ⏰ 时间策略方案:');
        console.log('   - 等待至少2-4小时后重试');
        console.log('   - 在非工作时间（晚上/周末）重试');
        console.log('   - 间隔24小时后重新尝试');
        
        console.log('\n4. 🔄 浏览器重置方案:');
        console.log('   - 执行 clearBrowserFingerprint() 清理浏览器指纹');
        console.log('   - 清除所有京麦相关的存储数据');
        console.log('   - 重置浏览器配置文件');
        
        console.groupEnd();
        
        return {
            solution1: '网络切换（最推荐）',
            solution2: '设备环境更换',
            solution3: '时间延迟策略',
            solution4: '浏览器指纹重置',
            quickTest: '运行 testNetworkConnection() 测试网络状态'
        };
    };

    // 浏览器指纹清理工具
    window.clearBrowserFingerprint = function() {
        try {
            console.log('🧹 开始清理浏览器指纹...');
            
            // 清除所有本地存储
            localStorage.clear();
            sessionStorage.clear();
            
            // 清除所有京麦相关cookies
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            // 清除GM脚本数据（非配置数据）
            GM_setValue('cached_real_response', null);
            GM_setValue('scrape_logs', []);
            
            console.log('✅ 浏览器指纹清理完成');
            console.log('💡 建议：重启浏览器或使用无痕模式访问京麦');
            
            return '指纹清理完成，请重启浏览器';
        } catch (error) {
            console.error('❌ 指纹清理失败:', error);
            return '清理失败: ' + error.message;
        }
    };

    // 网络连接测试工具
    window.testNetworkConnection = function() {
        console.log('🌐 测试网络连接状态...');
        
        // 获取当前IP信息
        fetch('https://ipapi.co/json/')
            .then(response => response.json())
            .then(data => {
                console.log('📍 当前网络信息:');
                console.log('- IP地址:', data.ip);
                console.log('- 位置:', `${data.city}, ${data.country_name}`);
                console.log('- ISP:', data.org);
                console.log('- 时区:', data.timezone);
            })
            .catch(error => {
                console.log('⚠️ 无法获取IP信息:', error.message);
            });
            
        // 测试京麦连接
        fetch('https://shop.jd.com', {method: 'HEAD'})
            .then(response => {
                console.log('🔗 京麦连接状态:', response.status === 200 ? '正常' : '异常');
            })
            .catch(error => {
                console.log('❌ 京麦连接失败:', error.message);
            });
            
        return '网络测试已启动，请查看控制台结果';
    };

    // 智能重试工具（超保守模式）
    window.smartRetryMode = function() {
        console.log('🎯 启动智能重试模式（超保守）');
        
        const retryConfig = {
            baseDelay: 3600000,      // 1小时基础延迟
            maxDelay: 14400000,      // 最大4小时延迟  
            maxRetries: 3,           // 最多3次重试
            currentRetry: 0
        };
        
        const executeRetry = async (retryCount) => {
            const delay = Math.min(
                retryConfig.baseDelay * Math.pow(2, retryCount) + Math.random() * 1800000,
                retryConfig.maxDelay
            );
            
            console.log(`⏳ 第${retryCount + 1}次重试将在 ${Math.round(delay/60000)} 分钟后执行`);
            
            // 设置定时器
            setTimeout(async () => {
                try {
                    console.log(`🚀 开始第${retryCount + 1}次重试`);
                    
                    // 先测试网络环境
                    await testNetworkConnection();
                    
                    // 清理环境
                    clearBrowserFingerprint();
                    
                    console.log('✅ 环境准备完成，可以手动尝试操作页面');
                    GM_notification('智能重试', `第${retryCount + 1}次重试已准备完成，请手动操作`, 'info');
                    
                } catch (error) {
                    console.error(`❌ 第${retryCount + 1}次重试失败:`, error);
                    
                    if (retryCount < retryConfig.maxRetries - 1) {
                        executeRetry(retryCount + 1);
                    } else {
                        console.log('❌ 所有重试已用完，建议更换设备或网络环境');
                        GM_notification('重试完成', '建议更换设备或网络环境', 'error');
                    }
                }
            }, delay);
        };
        
        executeRetry(0);
        return `智能重试模式已启动，共${retryConfig.maxRetries}次重试机会`;
    };

    // 更新脚本状态
    window.jingmaiScriptStatus = 'loaded';
    console.log('✅ [京麦脚本] 主脚本执行完成');
    console.log('🧪 [调试方法] 现在可以使用以下命令：');
    console.log('  - checkJingmaiData() - 查看存储数据');
    console.log('  - checkJingmaiIntercept() - 检查拦截状态'); 
    console.log('  - clearJingmaiData() - 清理数据重新开始');
    console.log('  - reinitJingmaiIntercept() - 重新初始化拦截');
    console.log('🚫 [设备限制解决方案] 以下是处理设备被限制的工具：');
    console.log('  - deviceBlockSolution() - 查看完整解决方案指南');
    console.log('  - clearBrowserFingerprint() - 清理浏览器指纹');
    console.log('  - testNetworkConnection() - 测试网络环境');
    console.log('  - smartRetryMode() - 启动智能重试模式（超保守）');

})();

console.log('🎉 [京麦脚本] 脚本加载完成！');
console.log('🧪 [京麦脚本] 请运行 testJingmaiScriptLoaded() 确认脚本状态');
console.log('📊 [重要] 拦截器已自动启动，请先运行 showProgressPanel() 显示进度面板，然后在页面操作触发数据抓取');
console.log('🧪 [调试命令] 现在可以使用以下调试命令：');
console.log('  📊 数据查看:');
console.log('    - checkInterceptedData() - 查看拦截到的数据（推荐！）');
console.log('    - checkJingmaiData() - 查看存储数据');
console.log('    - checkJingmaiIntercept() - 检查拦截状态');
console.log('  🎛️ 界面控制:');
console.log('    - showProgressPanel() - 显示进度面板');
console.log('    - hideProgressPanel() - 隐藏进度面板');
console.log('  📤 数据操作:');
console.log('    - previewUploadData() - 预览转换后的上传数据格式');
console.log('    - uploadNow() - 立即上传当前数据');
console.log('    - clearData() - 清空已抓取的数据');
console.log('    - resetAndRestart() - 清空所有数据并重新开始抓取');
console.log('  🔧 脚本控制:');
console.log('    - startJingmaiIntercept() - 检查拦截器状态');
console.log('    - clearJingmaiData() - 清理存储数据');
console.log('    - reinitJingmaiIntercept() - 重新初始化拦截');
console.log('🚫 [设备限制] 如果遇到请求被限制的问题：');
console.log('  - deviceBlockSolution() - 查看解决方案（重要！）');
console.log('  - clearBrowserFingerprint() - 清理浏览器指纹');
console.log('  - testNetworkConnection() - 测试网络状态');
console.log('  - smartRetryMode() - 启动超保守重试模式');