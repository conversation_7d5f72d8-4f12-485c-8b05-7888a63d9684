// ==UserScript==
// @name         京麦数据抓取脚本 v1.0.1
// @namespace    jingmai-data-scraper
// @version      1.0.1
// @description  从京麦系统抓取VC账户信息和资质数据，同步到资质管理系统（已移除remark字段）
// <AUTHOR>
// @match        https://shop.jd.com/jdm/home*
// @match        https://seller-v10.shop.jd.com/seller-supplier/quaMenu*
// @require      https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_notification
// @grant        GM_log
// @run-at       document-idle
// ==/UserScript==

// ===== 脚本加载测试 =====
console.log('🚀 [京麦脚本] 开始加载京麦数据抓取脚本 v1.0.1 (已移除remark字段)');

// 立即暴露一个简单的测试函数，确认脚本是否加载
window.testJingmaiScriptLoaded = function() {
    console.log('✅ [京麦脚本] 脚本已成功加载！');
    console.log('📍 [京麦脚本] 当前页面:', window.location.href);
    console.log('⏰ [京麦脚本] 加载时间:', new Date().toISOString());
    return '京麦脚本已加载';
};

console.log('🎯 [京麦脚本] 可以运行 testJingmaiScriptLoaded() 来测试脚本是否加载');

// ===== 早期全局方法暴露（防止主脚本出错）=====
try {
    window.jingmaiScriptStatus = 'loading';

    // 基础的测试方法
    window.basicJingmaiTest = function() {
        console.log('🧪 [基础测试] 京麦脚本基础功能测试');
        console.log('📍 当前页面:', window.location.href);
        console.log('⏰ 当前时间:', new Date().toISOString());

        // 检查页面类型
        const url = window.location.href;
        let pageType = 'unknown';
        if (url.includes('shop.jd.com/jdm/home')) {
            pageType = 'account';
        } else if (url.includes('seller-v10.shop.jd.com/seller-supplier/quaMenu')) {
            pageType = 'qualification';
        }

        console.log('📄 页面类型:', pageType);

        // 检查常见的h5st对象
        const h5stCheck = ['_JdJrTdRiskFpInfo', 'jdJrRiskFpInfo', 'generateH5st'].map(name => ({
            name,
            exists: !!window[name],
            type: typeof window[name]
        }));

        console.log('🔧 H5ST对象检查:', h5stCheck);

        return {
            pageType,
            h5stObjects: h5stCheck.filter(obj => obj.exists),
            timestamp: Date.now()
        };
    };

    console.log('✅ [京麦脚本] 基础测试方法已暴露: basicJingmaiTest()');

} catch (error) {
    console.error('❌ [京麦脚本] 早期方法暴露失败:', error);
}

(function() {
    'use strict';

    // ===== GM函数兼容性检查 =====
    if (typeof GM_getValue === 'undefined') {
        console.error('GM_getValue 函数不可用，脚本可能无法正常工作');
    }
    if (typeof GM_setValue === 'undefined') {
        console.error('GM_setValue 函数不可用，脚本可能无法正常工作');
    }
    if (typeof GM_notification === 'undefined') {
        // 如果GM_notification不可用，提供一个备用实现
        window.GM_notification = function(title, text, image) {
            console.log(`[${title}] ${text}`);
            if (typeof alert !== 'undefined') {
                alert(`${title}: ${text}`);
            }
        };
    }

    // ===== 配置模块 =====
    
    // 一次性更新API地址（处理从旧地址迁移）
    (() => {
        const newDefaultUrl = 'http://43.137.41.125:8088/admin-api/qualification/sync';
        const oldUrls = [
            'http://127.0.0.1:48080/admin-api/qualification/sync',
            'http://127.0.0.1:28080/admin-api/qualification/sync'
        ];
        const currentUrl = GM_getValue('API_BASE_URL', '');
        
        // 如果当前是旧地址或者为空，更新为新地址
        if (!currentUrl || oldUrls.includes(currentUrl)) {
            GM_setValue('API_BASE_URL', newDefaultUrl);
            console.log(`[京麦数据抓取脚本] API地址已更新为: ${newDefaultUrl}`);
        }
    })();
    
    const CONFIG = {
        // API配置
        API_BASE_URL: GM_getValue('API_BASE_URL', 'http://43.137.41.125:8088/admin-api/qualification/sync'),
        API_TOKEN: GM_getValue('API_TOKEN', ''),
        
        // 抓取配置
        AUTO_SCRAPE: GM_getValue('AUTO_SCRAPE', true), // 默认开启自动同步
        SCRAPE_INTERVAL: GM_getValue('SCRAPE_INTERVAL', 60000), // 60秒检查一次
        MAX_RETRY_COUNT: GM_getValue('MAX_RETRY_COUNT', 3),
        BATCH_SIZE: GM_getValue('BATCH_SIZE', 10),
        FIRST_SETUP_DONE: GM_getValue('FIRST_SETUP_DONE', false), // 是否完成初次设置
        MANAGER_NAME: GM_getValue('MANAGER_NAME', ''), // 管理员姓名
        
        // 界面配置
        PANEL_POSITION: GM_getValue('PANEL_POSITION', 'top-right'),
        SHOW_LOGS: GM_getValue('SHOW_LOGS', true),
        
        // 京麦接口配置
        JINGMAI_APIS: {
            ACCOUNT_INFO: 'https://sff.jd.com/api?v=1.0&appId=1VLIXQT5B1IEYHXSVHRV&api=dsm.shop.pageframe.navigation.accountFacade.findAccountInfo',
            QUALIFICATION_LIST: 'https://sff.jd.com/api?v=1.0&appId=XRICZR1JYFQENYERSMUP&api=dsm.omni.qua.product.BrandRelationReadService.queryBrandRelationFormalList'
        }
    };
    
    // ===== 工具模块 =====
    const Utils = {
        // 日志记录
        log: function(level, message, data = null) {
            const timestamp = new Date().toISOString();
            const logMessage = `[京麦脚本] [${level}] ${message}`;

            // 简化控制台输出，只显示重要信息
            if (level === 'ERROR') {
                console.error(logMessage, data || '');
            } else if (level === 'WARN') {
                console.warn(logMessage);
            } else if (level === 'INFO' && (
                message.includes('同步成功') ||
                message.includes('上传完成') ||
                message.includes('抓取完成') ||
                message.includes('初始化完成')
            )) {
                console.log(logMessage);
            }
            // 其他INFO级别日志不在控制台显示，只存储

            GM_log(logMessage + (data ? JSON.stringify(data) : ''));

            // 存储日志到本地
            const logs = GM_getValue('scrape_logs', []);
            logs.push({
                timestamp,
                level,
                message,
                data
            });

            // 只保留最近1000条日志
            if (logs.length > 1000) {
                logs.splice(0, logs.length - 1000);
            }

            GM_setValue('scrape_logs', logs);
        },
        
        // 延迟执行
        delay: function(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },
        
        // 格式化日期为YYYY-MM-DD格式
        formatDate: function(timestamp) {
            if (!timestamp) return '';
            
            let date;
            if (typeof timestamp === 'number') {
                date = new Date(timestamp);
            } else if (typeof timestamp === 'string') {
                // 处理多种日期格式
                if (timestamp.includes('/')) {
                    // 处理 2026/12/23 格式
                    const parts = timestamp.split('/');
                    if (parts.length === 3) {
                        date = new Date(parts[0], parts[1] - 1, parts[2]);
                    }
                } else {
                    date = new Date(timestamp);
                }
            } else {
                date = new Date(timestamp);
            }
            
            if (isNaN(date.getTime())) {
                return '';
            }
            
            // 返回 YYYY-MM-DD 格式
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        
        // 生成UUID
        generateUUID: function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        },
        
        // 检查页面类型
        getPageType: function() {
            const url = window.location.href;
            if (url.includes('shop.jd.com/jdm/home')) {
                return 'account';
            } else if (url.includes('seller-v10.shop.jd.com/seller-supplier/quaMenu')) {
                return 'qualification';
            }
            return 'unknown';
        }
    };
    
    // ===== H5ST生成器模块 =====
    const H5stGenerator = {
        // 查找页面中的h5st生成函数
        findH5stFunction: function() {
            console.log('🔍 [H5ST] 开始查找页面中的h5st生成函数');

            // 常见的h5st相关全局对象名称
            const possibleNames = [
                '_JdJrTdRiskFpInfo',
                'jdJrRiskFpInfo',
                'generateH5st',
                'getH5st',
                'h5st',
                'JdJrTdRiskFpInfo',
                'risk',
                'fp',
                'fingerprint',
                'jdRisk'
            ];

            // 遍历可能的函数名
            for (let name of possibleNames) {
                if (window[name] && typeof window[name] === 'object') {
                    console.log(`🎯 [H5ST] 找到可能的h5st对象: ${name}`, window[name]);

                    // 检查是否有getData方法
                    if (typeof window[name].getData === 'function') {
                        console.log(`✅ [H5ST] 找到h5st生成函数: ${name}.getData`);
                        return { obj: window[name], method: 'getData', name: name };
                    }

                    // 检查是否有getH5st方法
                    if (typeof window[name].getH5st === 'function') {
                        console.log(`✅ [H5ST] 找到h5st生成函数: ${name}.getH5st`);
                        return { obj: window[name], method: 'getH5st', name: name };
                    }
                }
            }

            // 深度搜索全局对象
            return this.deepSearchH5stFunction();
        },

        // 深度搜索h5st函数
        deepSearchH5stFunction: function() {
            console.log('🔍 [H5ST] 开始深度搜索h5st函数');

            const searchedObjects = new Set();

            const searchObject = (obj, path = 'window') => {
                if (!obj || typeof obj !== 'object' || searchedObjects.has(obj)) {
                    return null;
                }

                searchedObjects.add(obj);

                // 检查当前对象的方法
                for (let key in obj) {
                    try {
                        const value = obj[key];

                        // 检查是否是h5st相关方法
                        if (typeof value === 'function') {
                            const funcStr = value.toString();
                            if (funcStr.includes('h5st') ||
                                funcStr.includes('H5ST') ||
                                (funcStr.includes('timestamp') && funcStr.includes('signature'))) {
                                console.log(`🎯 [H5ST] 找到可能的h5st函数: ${path}.${key}`);
                                return { obj, method: key, path: `${path}.${key}` };
                            }
                        }

                        // 递归搜索子对象（限制深度）
                        if (typeof value === 'object' && value !== null && path.split('.').length < 4) {
                            const result = searchObject(value, `${path}.${key}`);
                            if (result) return result;
                        }
                    } catch (e) {
                        // 忽略访问错误
                    }
                }

                return null;
            };

            return searchObject(window);
        },

        // 获取h5st参数
        getH5st: async function(requestParams) {
            console.log('🔧 [H5ST] 开始生成h5st参数');

            // 首先尝试查找现有函数
            const h5stObj = this.findH5stFunction();

            if (h5stObj) {
                try {
                    // 准备参数，增加更多的参数以提高成功率
                    const params = this.prepareH5stParams(requestParams);
                    console.log('📤 [H5ST] 准备的参数:', params);

                    // 调用生成函数，增加重试机制
                    let result = null;
                    const maxRetries = 3;
                    
                    for (let i = 0; i < maxRetries; i++) {
                        try {
                            result = h5stObj.obj[h5stObj.method](params);

                            // 如果返回Promise，等待结果
                            if (result && typeof result.then === 'function') {
                                result = await result;
                            }

                            // 验证结果的有效性
                            if (this.validateH5st(result)) {
                                console.log('✅ [H5ST] h5st生成成功，验证通过');
                                break;
                            } else {
                                console.warn(`⚠️ [H5ST] 第${i + 1}次生成的h5st验证失败，重试...`);
                                result = null;
                            }
                        } catch (error) {
                            console.warn(`⚠️ [H5ST] 第${i + 1}次生成失败:`, error);
                            if (i < maxRetries - 1) {
                                await this.delay(1000); // 重试前等待1秒
                            }
                        }
                    }

                    console.log('📥 [H5ST] 最终生成结果:', result);

                    // 提取h5st字符串
                    if (typeof result === 'string') {
                        return result;
                    } else if (result && result.h5st) {
                        return result.h5st;
                    } else if (result && result.data) {
                        return result.data;
                    }

                    return result;

                } catch (error) {
                    console.error('❌ [H5ST] 调用h5st函数失败:', error);
                }
            }

            // 如果找不到函数，尝试其他方法
            return await this.alternativeH5stGeneration(requestParams);
        },

        // 验证H5ST的有效性
        validateH5st: function(h5st) {
            if (!h5st) return false;
            
            const h5stStr = typeof h5st === 'string' ? h5st : 
                           (h5st.h5st || h5st.data || '');
            
            // 基本格式验证
            if (typeof h5stStr !== 'string' || h5stStr.length < 10) {
                return false;
            }
            
            // 检查是否包含必要的组件（时间戳、签名等）
            if (!h5stStr.includes('&') || !h5stStr.includes('=')) {
                return false;
            }
            
            console.log('📊 [H5ST验证] h5st格式验证通过，长度:', h5stStr.length);
            return true;
        },

        // 延迟函数
        delay: function(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },

        // 准备h5st生成所需的参数
        prepareH5stParams: function(requestParams) {
            const baseParams = {
                appId: 'XRICZR1JYFQENYERSMUP',
                api: 'dsm.omni.qua.product.BrandRelationReadService.queryBrandRelationFormalList',
                version: '1.0',
                timestamp: Date.now(),
                url: window.location.href,
                method: 'POST'
            };

            // 如果有请求参数，添加到data中
            if (requestParams) {
                baseParams.data = requestParams;
                baseParams.body = typeof requestParams === 'string' ? requestParams : JSON.stringify(requestParams);
            }

            return baseParams;
        },

        // 备用h5st生成方法
        alternativeH5stGeneration: async function(requestParams) {
            console.log('🔄 [H5ST] 尝试备用h5st生成方法');

            try {
                // 方法1：通过脚本注入获取
                const h5st = await this.getH5stViaInjection(requestParams);
                if (h5st) return h5st;

                // 方法2：拦截页面原生请求
                return await this.interceptNativeRequest(requestParams);
            } catch (error) {
                console.error('❌ [H5ST] 备用方法失败:', error);
                return null;
            }
        },

        // 通过脚本注入获取h5st
        getH5stViaInjection: function(requestParams) {
            return new Promise((resolve, reject) => {
                console.log('💉 [H5ST] 开始脚本注入方法');

                // 检查是否已经注入过脚本
                if (!window.h5stInjected) {
                    this.injectH5stScript();
                    window.h5stInjected = true;
                }

                const requestId = Date.now().toString();

                // 监听页面返回的消息
                const messageHandler = (event) => {
                    if (event.data && event.data.type === 'H5ST_RESULT' && event.data.requestId === requestId) {
                        window.removeEventListener('message', messageHandler);
                        console.log('✅ [H5ST] 通过注入获取到h5st:', event.data.h5st);
                        resolve(event.data.h5st);
                    }
                };

                window.addEventListener('message', messageHandler);

                // 发送获取h5st的请求
                window.postMessage({
                    type: 'GET_H5ST',
                    params: this.prepareH5stParams(requestParams),
                    requestId: requestId
                }, '*');

                // 超时处理
                setTimeout(() => {
                    window.removeEventListener('message', messageHandler);
                    console.warn('⏰ [H5ST] 脚本注入方法超时');
                    resolve(null);
                }, 5000);
            });
        },

        // 注入h5st获取脚本到页面
        injectH5stScript: function() {
            console.log('💉 [H5ST] 注入h5st获取脚本到页面');

            const script = document.createElement('script');
            script.textContent = `
                // 在页面环境中运行的代码
                (function() {
                    console.log('🔧 [H5ST注入] 脚本已在页面环境中运行');

                    window.getH5stForTampermonkey = function(params) {
                        try {
                            console.log('🔧 [H5ST注入] 开始在页面环境中生成h5st', params);

                            // 查找h5st生成函数
                            const generators = [
                                window._JdJrTdRiskFpInfo,
                                window.jdJrRiskFpInfo,
                                window.generateH5st,
                                window.jdRisk,
                                window.risk
                            ];

                            for (let generator of generators) {
                                if (generator && typeof generator === 'object') {
                                    console.log('🎯 [H5ST注入] 找到生成器对象:', generator);

                                    if (typeof generator.getData === 'function') {
                                        console.log('📞 [H5ST注入] 调用getData方法');
                                        const result = generator.getData(params);
                                        console.log('📥 [H5ST注入] getData结果:', result);
                                        return result;
                                    }

                                    if (typeof generator.getH5st === 'function') {
                                        console.log('📞 [H5ST注入] 调用getH5st方法');
                                        const result = generator.getH5st(params);
                                        console.log('📥 [H5ST注入] getH5st结果:', result);
                                        return result;
                                    }
                                }
                            }

                            console.warn('⚠️ [H5ST注入] 未找到有效的h5st生成函数');
                            return null;
                        } catch (error) {
                            console.error('❌ [H5ST注入] 页面环境h5st生成失败:', error);
                            return null;
                        }
                    };

                    // 监听来自Tampermonkey的消息
                    window.addEventListener('message', function(event) {
                        if (event.data && event.data.type === 'GET_H5ST') {
                            console.log('📨 [H5ST注入] 收到h5st生成请求:', event.data);

                            const h5st = window.getH5stForTampermonkey(event.data.params);

                            window.postMessage({
                                type: 'H5ST_RESULT',
                                h5st: h5st,
                                requestId: event.data.requestId
                            }, '*');
                        }
                    });

                    console.log('✅ [H5ST注入] 消息监听器已设置');
                })();
            `;

            document.head.appendChild(script);
            console.log('✅ [H5ST] h5st获取脚本已注入到页面');
        },

        // 拦截页面原生请求获取h5st
        interceptNativeRequest: function(requestParams) {
            return new Promise((resolve, reject) => {
                console.log('🎣 [H5ST] 开始拦截原生请求获取h5st');

                // 保存原始的XMLHttpRequest和fetch
                const originalXHR = window.XMLHttpRequest;
                const originalFetch = window.fetch;
                const originalOpen = originalXHR.prototype.open;
                const originalSend = originalXHR.prototype.send;

                let intercepted = false;

                // 重写XMLHttpRequest
                const newXHR = function() {
                    const xhr = new originalXHR();

                    xhr.open = function(method, url, ...args) {
                        this._method = method;
                        this._url = url;
                        this._headers = {};
                        return originalOpen.apply(this, [method, url, ...args]);
                    };

                    xhr.setRequestHeader = function(name, value) {
                        this._headers[name] = value;
                        if (name.toLowerCase() === 'h5st' && this._url && this._url.includes('queryBrandRelationFormalList')) {
                            console.log('🎯 [H5ST] 拦截到h5st参数:', value);
                            intercepted = true;
                            resolve(value);

                            // 恢复原始函数
                            window.XMLHttpRequest = originalXHR;
                            window.fetch = originalFetch;
                        }
                        return originalXHR.prototype.setRequestHeader.apply(this, [name, value]);
                    };

                    return xhr;
                };

                // 重写fetch
                window.fetch = function(url, options = {}) {
                    if (typeof url === 'string' && url.includes('queryBrandRelationFormalList')) {
                        const h5st = options.headers?.h5st || options.headers?.H5st;
                        if (h5st) {
                            console.log('🎯 [H5ST] 从fetch请求中拦截到h5st:', h5st);
                            intercepted = true;
                            resolve(h5st);

                            // 恢复原始函数
                            window.XMLHttpRequest = originalXHR;
                            window.fetch = originalFetch;
                        }
                    }
                    return originalFetch.apply(this, arguments);
                };

                window.XMLHttpRequest = newXHR;

                // 触发一个真实的请求
                setTimeout(() => {
                    this.triggerNativeRequest(requestParams);
                }, 1000);

                // 超时处理
                setTimeout(() => {
                    if (!intercepted) {
                        window.XMLHttpRequest = originalXHR;
                        window.fetch = originalFetch;
                        console.warn('⏰ [H5ST] 拦截原生请求超时');
                        resolve(null);
                    }
                }, 10000);
            });
        },

        // 触发页面原生请求
        triggerNativeRequest: function(requestParams) {
            console.log('🚀 [H5ST] 触发页面原生请求');

            try {
                // 方法1：模拟点击页面元素
                const searchBtn = document.querySelector('button[type="submit"], .search-btn, .query-btn, .btn-search');
                if (searchBtn && searchBtn.offsetParent !== null) { // 确保元素可见
                    console.log('🖱️ [H5ST] 模拟点击搜索按钮');
                    searchBtn.click();
                    return;
                }

                // 方法2：查找分页按钮
                const pageBtn = document.querySelector('.ant-pagination-next, .pagination-next, .next-page');
                if (pageBtn && pageBtn.offsetParent !== null) {
                    console.log('🖱️ [H5ST] 模拟点击分页按钮');
                    pageBtn.click();
                    return;
                }

                // 方法3：触发页面事件
                const event = new Event('search', { bubbles: true });
                document.dispatchEvent(event);

                // 方法4：调用页面的查询函数
                if (window.search || window.query || window.loadData) {
                    const func = window.search || window.query || window.loadData;
                    if (typeof func === 'function') {
                        console.log('📞 [H5ST] 调用页面查询函数');
                        func(requestParams);
                    }
                }

            } catch (error) {
                console.error('❌ [H5ST] 触发原生请求失败:', error);
            }
        }
    };

    // ===== 数据抓取模块 =====
    const DataScraper = {
        // 初始化请求拦截
        init: function() {
            // 启用防检测机制
            this.setupAntiDetection();

            this.interceptPageRequests();
            this.cachedRealRequestData = null;
            this.interceptRetryCount = 0;
            this.maxInterceptRetries = 5; // 增加重试次数
            this.lastReinitTime = 0; // 上次重新初始化时间

            // 启用DOM观察器
            this.setupDOMObserver();

            // 延迟重新初始化拦截，防止页面动态加载导致拦截失效
            setTimeout(() => {
                this.reinitializeIntercept();
            }, 3000);

            // 定期检查拦截状态（降低频率）
            let lastPeriodicCheck = 0;
            setInterval(() => {
                const now = Date.now();
                // 避免过于频繁的检查
                if (now - lastPeriodicCheck < 30000) { // 至少30秒间隔
                    return;
                }

                const status = this.checkInterceptStatus();
                if (!status.hasCachedData && status.pageType === 'qualification') {
                    console.log('🔄 [京麦拦截] 定期检查：在资质页面但无缓存数据，重新初始化');
                    this.reinitializeIntercept();
                    lastPeriodicCheck = now;
                }
            }, 30000); // 每30秒检查一次

            Utils.log('INFO', '数据抓取模块已初始化，开始监听页面请求');
        },

        // 设置防检测机制
        setupAntiDetection: function() {
            console.log('🛡️ [防检测] 启用防检测机制');

            // 1. 拦截和修改上报数据
            this.interceptReportingRequests();

            // 2. 模拟正常用户行为
            this.simulateNormalBehavior();

            // 3. 隐藏自动化特征
            this.hideAutomationSignatures();
        },

        // 拦截上报请求
        interceptReportingRequests: function() {
            const originalXHR = window.XMLHttpRequest;
            const originalSend = originalXHR.prototype.send;

            originalXHR.prototype.send = function(data) {
                // 检查是否是上报请求
                if (this._url && (
                    this._url.includes('log') ||
                    this._url.includes('report') ||
                    this._url.includes('track') ||
                    this._url.includes('analytics') ||
                    this._url.includes('monitor')
                )) {
                    console.log('🛡️ [防检测] 拦截上报请求:', this._url);

                    // 修改上报数据，移除可疑特征
                    if (data && typeof data === 'string') {
                        try {
                            const reportData = JSON.parse(data);

                            // 修改可疑的用户代理信息
                            if (reportData.base && reportData.base.ua) {
                                // 确保UA看起来正常
                                reportData.base.ua = reportData.base.ua.replace(/HeadlessChrome|PhantomJS|Selenium/gi, 'Chrome');
                            }

                            // 修改设备信息，让它看起来更真实
                            if (reportData.base) {
                                reportData.base.isUa = true;
                                reportData.base.typ = 'web';
                                reportData.base.ope = 'reload';
                            }

                            data = JSON.stringify(reportData);
                            console.log('🛡️ [防检测] 已修改上报数据');
                        } catch (e) {
                            console.log('🛡️ [防检测] 上报数据解析失败，保持原样');
                        }
                    }
                }

                return originalSend.call(this, data);
            };
        },

        // 模拟正常用户行为
        simulateNormalBehavior: function() {
            // 定期模拟鼠标移动
            setInterval(() => {
                const event = new MouseEvent('mousemove', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight,
                    bubbles: true
                });
                document.dispatchEvent(event);
            }, 5000 + Math.random() * 10000); // 5-15秒随机间隔

            // 定期模拟滚动
            setInterval(() => {
                const scrollAmount = Math.random() * 200 - 100;
                window.scrollBy(0, scrollAmount);
            }, 8000 + Math.random() * 12000); // 8-20秒随机间隔

            // 模拟键盘活动
            setInterval(() => {
                const event = new KeyboardEvent('keydown', {
                    key: 'Tab',
                    bubbles: true
                });
                document.dispatchEvent(event);
            }, 15000 + Math.random() * 30000); // 15-45秒随机间隔
        },

        // 隐藏自动化特征
        hideAutomationSignatures: function() {
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // 修改插件信息
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5], // 模拟有插件
            });

            // 修改语言信息
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });

            console.log('🛡️ [防检测] 已隐藏自动化特征');
        },

        // 重新初始化拦截
        reinitializeIntercept: function() {
            const now = Date.now();

            // 避免过于频繁的重新初始化
            if (this.lastReinitTime && (now - this.lastReinitTime) < 5000) {
                console.log('🔄 [京麦拦截] 重新初始化冷却中，跳过');
                return;
            }

            if (this.interceptRetryCount < this.maxInterceptRetries) {
                this.interceptRetryCount++;
                this.lastReinitTime = now;

                console.log(`🔄 [京麦拦截] 重新初始化拦截机制 (第${this.interceptRetryCount}次)`);
                this.interceptPageRequests();

                // 只在前几次重试时继续定时重新初始化
                if (this.interceptRetryCount <= 2) {
                    setTimeout(() => {
                        this.reinitializeIntercept();
                    }, 10000);
                }
            } else {
                console.log('🔄 [京麦拦截] 已达到最大重试次数，停止重新初始化');
            }
        },

        // 拦截页面的真实请求
        interceptPageRequests: function() {
            const self = this;
            console.log('🔄 [京麦拦截] 开始拦截页面真实请求');

            // 监听页面中所有可能的请求方式
            this.interceptXMLHttpRequest();
            this.interceptFetch();
            this.interceptJQuery();
            
            // 启动全局拦截器
            this.setupGlobalInterceptor();

            Utils.log('INFO', '开始拦截页面真实请求');
        },
        
        // 设置全局拦截器（立即生效的简单拦截器）
        setupGlobalInterceptor: function() {
            const self = this;
            const TARGET_API = 'https://sff.jd.com/api?v=1.0&appId=XRICZR1JYFQENYERSMUP&api=dsm.omni.qua.product.BrandRelationReadService.queryBrandRelationFormalList';
            
            console.log('🎯 [全局拦截] 设置全局拦截器，目标API:', TARGET_API);
            
            // 保存原始fetch和XMLHttpRequest
            const originalFetch = window.fetch;
            const originalXHR = window.XMLHttpRequest;
            
            // 拦截fetch请求
            window.fetch = function(url, options = {}) {
                // 检查是否是目标API
                if (typeof url === 'string' && url.includes('queryBrandRelationFormalList')) {
                    console.group('🔍 [全局拦截] 拦截到Fetch资质查询请求');
                    console.log('📤 URL:', url);
                    console.log('📤 Options:', options);
                    console.groupEnd();
                    
                    // 保存请求信息
                    self.cachedRealRequestData = {
                        url: url,
                        method: options.method || 'POST',
                        headers: { ...options.headers },
                        body: options.body,
                        timestamp: Date.now(),
                        cookies: document.cookie,
                        userAgent: navigator.userAgent,
                        referer: window.location.href
                    };
                    
                    // 调用原始fetch并拦截响应
                    const promise = originalFetch.apply(this, arguments);
                    return promise.then(response => {
                        if (response.ok) {
                            response.clone().text().then(text => {
                                console.group('🔍 [全局拦截] Fetch响应详情');
                                console.log('📥 Status:', response.status);
                                console.log('📥 ResponseText:', text);
                                try {
                                    const data = JSON.parse(text);
                                    console.log('📥 ParsedData:', data);
                                    self.cacheRealResponse(data);
                                    
                                    // 立即通知UI更新
                                    if (window.UI && typeof window.UI.updateStatistics === 'function') {
                                        setTimeout(() => {
                                            window.UI.updateStatistics();
                                        }, 100);
                                    }
                                } catch (e) {
                                    console.error('📥 解析失败:', e);
                                }
                                console.groupEnd();
                            });
                        }
                        return response;
                    });
                }
                return originalFetch.apply(this, arguments);
            };
            
            // 拦截XMLHttpRequest
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                const originalSend = xhr.send;
                
                xhr.open = function(method, url, ...args) {
                    this._url = url;
                    this._method = method;
                    this._headers = {};
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                
                const originalSetRequestHeader = xhr.setRequestHeader;
                xhr.setRequestHeader = function(name, value) {
                    this._headers[name] = value;
                    return originalSetRequestHeader.apply(this, [name, value]);
                };
                
                xhr.send = function(data) {
                    const isTargetAPI = this._url && this._url.includes('queryBrandRelationFormalList');
                    
                    if (isTargetAPI) {
                        console.group('🔍 [全局拦截] 拦截到XHR资质查询请求');
                        console.log('📤 完整URL:', this._url);
                        console.log('📤 Method:', this._method);
                        console.log('📤 Headers:', this._headers);
                        console.log('📤 Body:', data);
                        console.groupEnd();
                        
                        // 保存请求信息
                        self.cachedRealRequestData = {
                            url: this._url,
                            method: this._method,
                            headers: { ...this._headers },
                            body: data,
                            timestamp: Date.now(),
                            cookies: document.cookie,
                            userAgent: navigator.userAgent,
                            referer: window.location.href
                        };
                        
                        // 监听响应
                        this.addEventListener('load', function() {
                            if (this.status === 200) {
                                console.group('🔍 [全局拦截] XHR响应详情');
                                console.log('📥 Status:', this.status);
                                console.log('📥 ResponseText:', this.responseText);
                                try {
                                    const response = JSON.parse(this.responseText);
                                    console.log('📥 ParsedData:', response);
                                    self.cacheRealResponse(response);
                                    
                                    // 立即通知UI更新
                                    if (window.UI && typeof window.UI.updateStatistics === 'function') {
                                        setTimeout(() => {
                                            window.UI.updateStatistics();
                                        }, 100);
                                    }
                                } catch (e) {
                                    console.error('📥 解析失败:', e);
                                }
                                console.groupEnd();
                            }
                        });
                    }
                    return originalSend.apply(this, [data]);
                };
                
                return xhr;
            };
            
            console.log('✅ [全局拦截] 全局拦截器设置完成');
        },

        // 拦截XMLHttpRequest
        interceptXMLHttpRequest: function() {
            const self = this;

            // 重写XMLHttpRequest
            const originalXHR = window.XMLHttpRequest;
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                const originalSend = xhr.send;
                
                xhr.open = function(method, url, ...args) {
                    this._url = url;
                    this._method = method;
                    this._headers = {};
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                
                // 拦截setRequestHeader
                const originalSetRequestHeader = xhr.setRequestHeader;
                xhr.setRequestHeader = function(name, value) {
                    this._headers[name] = value;
                    return originalSetRequestHeader.apply(this, [name, value]);
                };
                
                xhr.send = function(data) {
                    // 检查是否是目标接口
                    const isTargetAPI = this._url && (
                        this._url.includes('queryBrandRelationFormalList') ||
                        this._url.includes('dsm.omni.qua.product.BrandRelationReadService.queryBrandRelationFormalList')
                    );

                    if (isTargetAPI) {
                        // 详细打印请求信息
                        console.group('🔍 [京麦拦截] 资质查询请求详情');
                        console.log('📤 完整URL:', this._url);
                        console.log('📤 Method:', this._method);
                        console.log('📤 Headers:', this._headers);
                        console.log('📤 Body:', data);
                        console.log('📤 Timestamp:', new Date().toISOString());
                        console.log('📤 页面URL:', window.location.href);
                        console.groupEnd();

                        Utils.log('INFO', '检测到资质查询请求，记录真实参数');

                        // 保存真实请求数据
                        self.cachedRealRequestData = {
                            url: this._url,
                            method: this._method,
                            headers: { ...this._headers },
                            body: data,
                            timestamp: Date.now(),
                            cookies: document.cookie,
                            userAgent: navigator.userAgent,
                            referer: window.location.href
                        };

                        // 监听响应
                        this.addEventListener('load', function() {
                            // 详细打印响应信息
                            console.group('🔍 [京麦拦截] 资质查询响应详情');
                            console.log('📥 Status:', this.status);
                            console.log('📥 StatusText:', this.statusText);
                            console.log('📥 ResponseHeaders:', this.getAllResponseHeaders());
                            console.log('📥 ResponseText (前500字符):', this.responseText.substring(0, 500));
                            console.log('📥 ResponseText (完整):', this.responseText);
                            console.log('📥 Timestamp:', new Date().toISOString());

                            if (this.status === 200) {
                                try {
                                    const response = JSON.parse(this.responseText);
                                    console.log('📥 ParsedData:', response);

                                    // 特别关注数据结构
                                    if (response.data && response.data.itemList) {
                                        console.log('📥 资质数据条数:', response.data.itemList.length);
                                        console.log('📥 分页信息:', response.data.pageInfoFacet);
                                    }

                                    console.groupEnd();

                                    self.cacheRealResponse(response);
                                    Utils.log('INFO', '已缓存真实请求的响应数据');

                                    // 立即通知UI更新
                                    if (window.UI && typeof window.UI.updateStatistics === 'function') {
                                        setTimeout(() => {
                                            window.UI.updateStatistics();
                                        }, 100);
                                    }

                                } catch (e) {
                                    console.error('📥 解析失败:', e);
                                    console.groupEnd();
                                    Utils.log('WARN', '解析拦截响应失败', e.message);
                                }
                            } else {
                                console.error('📥 请求失败');
                                console.groupEnd();
                                Utils.log('ERROR', '资质查询请求失败', {
                                    status: this.status,
                                    statusText: this.statusText,
                                    response: this.responseText
                                });
                            }
                        });

                        // 监听错误
                        this.addEventListener('error', function() {
                            console.group('🔍 [京麦拦截] 请求错误');
                            console.error('📥 Network Error');
                            console.groupEnd();
                            Utils.log('ERROR', '网络请求错误');
                        });
                    }
                    return originalSend.apply(this, [data]);
                };
                
                return xhr;
            };
        },

        // 拦截Fetch请求
        interceptFetch: function() {
            const self = this;

            if (typeof window.fetch !== 'undefined') {
                const originalFetch = window.fetch;
                window.fetch = function(url, options = {}) {
                    const isTargetAPI = typeof url === 'string' && (
                        url.includes('queryBrandRelationFormalList') ||
                        url.includes('dsm.omni.qua.product.BrandRelationReadService.queryBrandRelationFormalList')
                    );

                    if (isTargetAPI) {
                        console.group('🔍 [京麦拦截] Fetch资质查询请求');
                        console.log('📤 URL:', url);
                        console.log('📤 Options:', options);
                        console.groupEnd();

                        self.cachedRealRequestData = {
                            url: url,
                            method: options.method || 'GET',
                            headers: { ...options.headers },
                            body: options.body,
                            timestamp: Date.now(),
                            cookies: document.cookie,
                            userAgent: navigator.userAgent,
                            referer: window.location.href
                        };

                        // 拦截响应
                        const promise = originalFetch.apply(this, arguments);
                        return promise.then(response => {
                            if (response.ok) {
                                response.clone().text().then(text => {
                                    console.group('🔍 [京麦拦截] Fetch响应');
                                    console.log('📥 Status:', response.status);
                                    console.log('📥 Response:', text);
                                    try {
                                        const data = JSON.parse(text);
                                        console.log('📥 ParsedData:', data);
                                        self.cacheRealResponse(data);
                                    } catch (e) {
                                        console.error('📥 解析失败:', e);
                                    }
                                    console.groupEnd();
                                });
                            }
                            return response;
                        });
                    }

                    return originalFetch.apply(this, arguments);
                };
            }
        },

        // 拦截jQuery请求
        interceptJQuery: function() {
            const self = this;

            // 等待jQuery加载
            const checkJQuery = () => {
                if (typeof window.$ !== 'undefined' && window.$.ajax) {
                    const originalAjax = window.$.ajax;
                    window.$.ajax = function(options) {
                        const isTargetAPI = options.url && (
                            options.url.includes('queryBrandRelationFormalList') ||
                            options.url.includes('dsm.omni.qua.product.BrandRelationReadService.queryBrandRelationFormalList')
                        );

                        if (isTargetAPI) {
                            console.group('🔍 [京麦拦截] jQuery资质查询请求');
                            console.log('📤 URL:', options.url);
                            console.log('📤 Options:', options);
                            console.groupEnd();

                            self.cachedRealRequestData = {
                                url: options.url,
                                method: options.type || options.method || 'GET',
                                headers: { ...options.headers },
                                body: options.data,
                                timestamp: Date.now(),
                                cookies: document.cookie,
                                userAgent: navigator.userAgent,
                                referer: window.location.href
                            };

                            // 拦截成功回调
                            const originalSuccess = options.success;
                            options.success = function(data, textStatus, jqXHR) {
                                console.group('🔍 [京麦拦截] jQuery响应');
                                console.log('📥 Data:', data);
                                console.log('📥 Status:', textStatus);
                                console.groupEnd();

                                self.cacheRealResponse(data);

                                if (originalSuccess) {
                                    originalSuccess.apply(this, arguments);
                                }
                            };
                        }

                        return originalAjax.apply(this, arguments);
                    };
                } else {
                    // jQuery还没加载，继续等待
                    setTimeout(checkJQuery, 1000);
                }
            };

            checkJQuery();
        },

        // 使用MutationObserver监听页面变化，确保拦截不会失效
        setupDOMObserver: function() {
            const self = this;
            let lastReinitTime = 0;
            const REINIT_COOLDOWN = 10000; // 10秒冷却时间

            // 监听页面内容变化
            const observer = new MutationObserver((mutations) => {
                const now = Date.now();

                // 检查是否在冷却时间内
                if (now - lastReinitTime < REINIT_COOLDOWN) {
                    return;
                }

                // 只关注重要的DOM变化
                let hasSignificantChange = false;
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否有重要的节点变化（如表格、列表等）
                        for (let node of mutation.addedNodes) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                const tagName = node.tagName?.toLowerCase();
                                if (tagName === 'table' || tagName === 'tbody' ||
                                    node.className?.includes('table') ||
                                    node.className?.includes('list') ||
                                    node.querySelector?.('table, .table, .list')) {
                                    hasSignificantChange = true;
                                    break;
                                }
                            }
                        }
                    }
                });

                if (hasSignificantChange) {
                    lastReinitTime = now;
                    setTimeout(() => {
                        console.log('🔄 [京麦拦截] 检测到重要页面变化，重新初始化拦截');
                        self.interceptPageRequests();
                    }, 2000);
                }
            });

            // 开始观察，但限制观察范围
            if (document.body) {
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: false, // 不监听属性变化
                    characterData: false // 不监听文本变化
                });

                console.log('🔄 [京麦拦截] DOM观察器已启动（优化版）');
            }
        },

        // 缓存真实响应数据
        cacheRealResponse: function(responseData) {
            const cachedData = {
                timestamp: Date.now(),
                response: responseData,
                requestData: this.cachedRealRequestData
            };
            
            GM_setValue('cached_real_response', cachedData);
            Utils.log('INFO', '已更新缓存的真实响应数据');
        },

        // 获取VC账户信息
        getAccountInfo: async function() {
            Utils.log('INFO', '开始获取VC账户信息');

            // 增加随机延迟，模拟手动操作 (2-5秒)
            const delay = Math.random() * 3000 + 2000;
            Utils.log('INFO', `等待${Math.round(delay)}ms后开始请求`);
            await Utils.delay(delay);

            try {
                const response = await this.makeRequest(CONFIG.JINGMAI_APIS.ACCOUNT_INFO, {
                    method: 'POST',
                    headers: this.getCommonHeaders(),
                    body: '{}'
                });
                
                if (response.code === 200 && response.data) {
                    Utils.log('INFO', '成功获取VC账户信息', response.data);
                    return this.transformAccountData(response.data);
                } else {
                    throw new Error(`获取账户信息失败: ${response.msg || '未知错误'}`);
                }
            } catch (error) {
                Utils.log('ERROR', '获取VC账户信息失败', error.message);
                throw error;
            }
        },
        
        // 获取资质列表数据 - 使用真实请求参数和500条每页
        getQualificationList: async function(pageNum = 1, pageSize = 500, retryCount = 0) {
            Utils.log('INFO', `开始获取资质列表数据 - 第${pageNum}页 ${retryCount > 0 ? `(重试第${retryCount}次)` : ''}，每页${pageSize}条`);
            
            // 获取缓存的真实请求数据
            const cachedRealData = GM_getValue('cached_real_response', null);
            
            // 如果有缓存的真实请求数据，优先使用
            if (cachedRealData && cachedRealData.requestData && Date.now() - cachedRealData.timestamp < 300000) { // 5分钟内有效
                Utils.log('INFO', '使用缓存的真实请求参数发起请求');
                return await this.makeRequestWithRealParams(pageNum, pageSize, cachedRealData.requestData, retryCount);
            } else {
                Utils.log('INFO', '没有有效的缓存请求参数，等待用户先手动点击一次或使用默认参数');
                return await this.makeRequestWithDefaultParams(pageNum, pageSize, retryCount);
            }
        },

        // 使用真实请求参数发起请求
        makeRequestWithRealParams: async function(pageNum, pageSize, realRequestData, retryCount) {
            // 超保守延迟策略 - 避免风控检测
            let baseDelay;
            if (pageNum === 1) {
                baseDelay = Math.random() * 60000 + 120000; // 120-180秒 (2-3分钟)
            } else {
                baseDelay = Math.random() * 120000 + 300000; // 300-420秒 (5-7分钟)
            }
            
            if (retryCount > 0) {
                baseDelay += Math.pow(2, retryCount) * 60000; // 指数退避：60秒、120秒、240秒
            }
            
            Utils.log('INFO', `使用真实参数请求第${pageNum}页，延迟${Math.round(baseDelay)}ms`);
            await Utils.delay(baseDelay);

            // 🔧 获取有效的h5st参数
            console.log('🔧 [H5ST] 开始为请求获取有效的h5st参数');
            try {
                const h5st = await H5stGenerator.getH5st(realRequestData.body);
                if (h5st) {
                    console.log('✅ [H5ST] 成功获取h5st参数，长度:', h5st.length);
                    // 更新请求头中的h5st
                    if (!realRequestData.headers) {
                        realRequestData.headers = {};
                    }
                    realRequestData.headers['h5st'] = h5st;
                    Utils.log('INFO', 'h5st参数已更新到请求头');
                } else {
                    console.warn('⚠️ [H5ST] 未能获取有效的h5st参数，使用原有参数');
                }
            } catch (error) {
                console.error('❌ [H5ST] 获取h5st参数失败:', error);
                console.log('🔄 [H5ST] 继续使用原有参数进行请求');
            }

            try {
                // 解析真实请求体
                let realRequestBody;
                try {
                    realRequestBody = JSON.parse(realRequestData.body);
                } catch (e) {
                    Utils.log('WARN', '解析真实请求体失败，使用默认参数');
                    return await this.makeRequestWithDefaultParams(pageNum, pageSize, retryCount);
                }
                
                // 修改分页参数
                if (realRequestBody.queryParam) {
                    realRequestBody.queryParam.pageNum = pageNum;
                    realRequestBody.queryParam.pageSize = pageSize;
                }
                
                // 添加时间戳避免缓存
                if (realRequestBody.accessContext) {
                    realRequestBody.accessContext.timestamp = Date.now();
                }
                
                // 使用真实的请求头
                const realHeaders = {
                    ...realRequestData.headers,
                    'Content-Type': 'application/json;charset=UTF-8',
                    'User-Agent': realRequestData.userAgent,
                    'Referer': realRequestData.referer,
                    'Cookie': realRequestData.cookies
                };
                
                const response = await this.makeRequest(realRequestData.url, {
                    method: realRequestData.method,
                    headers: realHeaders,
                    body: JSON.stringify(realRequestBody)
                });
                
                if (response.code === 200 && response.data) {
                    Utils.log('INFO', `成功获取资质列表数据(真实参数) - 第${pageNum}页`, {
                        totalCount: response.data.pageInfoFacet?.totalCount,
                        currentItems: response.data.itemList?.length
                    });
                    return response.data;
                } else {
                    throw new Error(`获取资质数据失败: ${response.msg || '未知错误'}`);
                }
                
            } catch (error) {
                return await this.handleRequestError(error, pageNum, pageSize, retryCount, 'real');
            }
        },

        // 使用默认参数发起请求
        makeRequestWithDefaultParams: async function(pageNum, pageSize, retryCount) {
            // 超保守延迟策略 - 避免风控检测
            let baseDelay;
            if (pageNum === 1) {
                baseDelay = Math.random() * 90000 + 180000; // 180-270秒 (3-4.5分钟)
            } else {
                baseDelay = Math.random() * 180000 + 420000; // 420-600秒 (7-10分钟)
            }
            
            if (retryCount > 0) {
                baseDelay += Math.pow(2, retryCount) * 120000; // 指数退避：120秒、240秒、480秒
            }
            
            Utils.log('INFO', `使用默认参数请求第${pageNum}页，延迟${Math.round(baseDelay)}ms`);
            await Utils.delay(baseDelay);
            
            try {
                const requestBody = {
                    queryParam: {
                        brandCode: null,
                        purchaserCode: null,
                        brandType: null,
                        auditState: null,
                        pageNum: pageNum,
                        pageSize: pageSize,
                        vendorCode: null
                    },
                    callerParam: {
                        verticalCode: "cn_retail_vc",
                        buid: 301,
                        tenantId: 1024,
                        site: "301",
                        terminal: "PC"
                    },
                    accessContext: {
                        source: "web",
                        timestamp: Date.now()
                    }
                };
                
                const response = await this.makeRequest(CONFIG.JINGMAI_APIS.QUALIFICATION_LIST, {
                    method: 'POST',
                    headers: this.getEnhancedHeaders(),
                    body: JSON.stringify(requestBody)
                });
                
                if (response.code === 200 && response.data) {
                    Utils.log('INFO', `成功获取资质列表数据(默认参数) - 第${pageNum}页`, {
                        totalCount: response.data.pageInfoFacet?.totalCount,
                        currentItems: response.data.itemList?.length
                    });
                    return response.data;
                } else {
                    throw new Error(`获取资质数据失败: ${response.msg || '未知错误'}`);
                }
                
            } catch (error) {
                return await this.handleRequestError(error, pageNum, pageSize, retryCount, 'default');
            }
        },

        // 统一错误处理
        handleRequestError: async function(error, pageNum, pageSize, retryCount, requestType) {
            const errorMessage = error.message;
            
            // 检查是否是频率限制错误
            if (errorMessage.includes('频繁') || errorMessage.includes('请求次数过于频繁') || errorMessage.includes('请求过多')) {
                const maxRetries = 2; // 减少重试次数，避免进一步触发风控
                if (retryCount < maxRetries) {
                    // 大幅增加重试延迟，模拟用户长时间离开
                    const retryDelay = Math.pow(2, retryCount + 1) * 300000 + Math.random() * 300000; // 10-20分钟
                    Utils.log('WARN', `检测到频率限制(${requestType})，将等待${Math.round(retryDelay/1000/60)}分钟后进行第${retryCount + 1}次重试`);
                    
                    // 在等待期间执行更多的用户行为模拟
                    this.simulateExtendedUserBehavior();
                    
                    await Utils.delay(retryDelay);
                    return await this.getQualificationList(pageNum, pageSize, retryCount + 1);
                } else {
                    Utils.log('ERROR', '频率限制重试次数已达上限，建议至少等待30分钟后再试');
                    throw new Error('请求被限制，建议等待30-60分钟后重试');
                }
            }
            
            // 检查其他类型的错误
            if (errorMessage.includes('签名') || errorMessage.includes('h5st')) {
                Utils.log('ERROR', 'H5ST签名验证失败，可能需要重新获取签名参数');
                throw new Error('签名验证失败，请刷新页面重试');
            }
            
            Utils.log('ERROR', `获取资质列表数据失败(${requestType})`, errorMessage);
            throw error;
        },

        // 增强版请求头 - 更贴近真实浏览器行为
        getEnhancedHeaders: function() {
            // 随机化一些请求头值以避免被识别
            const randomEdgeVersion = '138.0.' + Math.floor(Math.random() * 10) + '.' + Math.floor(Math.random() * 100);
            const randomChromeVersion = '138.0.' + Math.floor(Math.random() * 100) + '.' + Math.floor(Math.random() * 100);
            
            return {
                "accept": "application/json, text/plain, */*",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "cache-control": "no-cache",
                "content-type": "application/json;charset=UTF-8",
                "dsm-client-info": '{"terminal":"0"}',
                "dsm-lang": "zh-CN",
                "dsm-platform": "pc",
                "dsm-site": "",
                "dsm-file-path": "lineation-price",
                "origin": window.location.origin,
                "pragma": "no-cache",
                "referer": window.location.href,
                "sec-ch-ua": `"Not)A;Brand";v="8", "Chromium";v="${randomChromeVersion}", "Microsoft Edge";v="${randomEdgeVersion}"`,
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "user-agent": navigator.userAgent,
                "x-requested-with": "XMLHttpRequest",
                // 添加一些随机的自定义头部以混淆特征
                "x-client-timestamp": Date.now().toString(),
                "x-page-load-time": (Math.random() * 5000 + 2000).toFixed(0)
            };
        },

        // 手动检查拦截状态
        checkInterceptStatus: function() {
            console.group('🔍 [京麦拦截] 拦截状态检查');
            console.log('📊 缓存的请求数据:', this.cachedRealRequestData);
            console.log('📊 拦截重试次数:', this.interceptRetryCount);
            console.log('📊 当前页面URL:', window.location.href);
            console.log('📊 页面类型:', Utils.getPageType());

            // 检查页面中是否有相关的网络请求
            const performanceEntries = performance.getEntriesByType('resource');
            const relevantRequests = performanceEntries.filter(entry =>
                entry.name.includes('queryBrandRelationFormalList')
            );
            console.log('📊 相关网络请求:', relevantRequests);
            console.groupEnd();

            return {
                hasCachedData: !!this.cachedRealRequestData,
                retryCount: this.interceptRetryCount,
                pageType: Utils.getPageType(),
                relevantRequests: relevantRequests.length
            };
        },

        // 获取所有资质数据 - 采用更保守的分页策略避免风控
        getAllQualifications: async function() {
            Utils.log('INFO', '开始获取所有资质数据（采用保守分页策略避免风控）');

            // 先检查拦截状态
            const interceptStatus = this.checkInterceptStatus();
            console.log('🔍 [京麦拦截] 当前拦截状态:', interceptStatus);

            // 如果没有缓存数据且在资质页面，尝试重新初始化拦截
            if (!interceptStatus.hasCachedData && interceptStatus.pageType === 'qualification') {
                console.log('⚠️ [京麦拦截] 在资质页面但未找到缓存数据，尝试重新初始化拦截');
                this.reinitializeIntercept();

                // 等待一段时间让拦截生效
                await Utils.delay(2000);

                // 再次检查
                const newStatus = this.checkInterceptStatus();
                console.log('🔍 [京麦拦截] 重新初始化后状态:', newStatus);
            }

            try {
                const allQualifications = [];
                let currentPage = 1;
                const pageSize = 100; // 改为100条每页，减少单次请求压力
                
                // 获取第一页数据
                const firstPageData = await this.getQualificationList(currentPage, pageSize);
                allQualifications.push(...firstPageData.itemList);
                
                const totalPages = firstPageData.pageInfoFacet.totalPage;
                const totalCount = firstPageData.pageInfoFacet.totalCount;
                
                Utils.log('INFO', `总共${totalCount}条记录，${totalPages}页，每页${pageSize}条（采用保守策略）`);
                
                // 如果只有一页，直接返回
                if (totalPages <= 1) {
                    Utils.log('INFO', '只有一页数据，获取完成');
                    return allQualifications.map(item => this.transformQualificationData(item));
                }
                
                // 如果页数太多（超过10页），分批处理
                const maxPagesPerBatch = 5; // 每批最多5页
                let pagesProcessed = 1;
                
                while (pagesProcessed < totalPages) {
                    const batchEndPage = Math.min(pagesProcessed + maxPagesPerBatch, totalPages);
                    Utils.log('INFO', `开始处理第${pagesProcessed + 1}-${batchEndPage}页数据`);
                    
                    // 处理当前批次的页面
                    for (let currentPage = pagesProcessed + 1; currentPage <= batchEndPage; currentPage++) {
                        Utils.log('INFO', `正在获取第${currentPage}页数据（共${totalPages}页），当前已获取${allQualifications.length}条`);

                        try {
                            const pageData = await this.getQualificationList(currentPage, pageSize);
                            allQualifications.push(...pageData.itemList);

                            // 更新UI进度
                            UI.updateProgress(currentPage, totalPages);
                            
                            // 每页之间增加适当的延迟
                            if (currentPage < batchEndPage) {
                                const pageDelay = 30000 + Math.random() * 15000; // 30-45秒
                                Utils.log('INFO', `第${currentPage}页获取完成，休息${Math.round(pageDelay/1000)}秒后继续`);
                                this.simulateUserBehavior();
                                await Utils.delay(pageDelay);
                            }
                            
                        } catch (error) {
                            if (error.message.includes('频率限制')) {
                                Utils.log('WARN', `第${currentPage}页请求被限制，已获取${allQualifications.length}条数据`);
                                // 跳出当前批次处理
                                break;
                            } else {
                                throw error;
                            }
                        }
                    }
                    
                    pagesProcessed = batchEndPage;
                    
                    // 如果还有更多批次，在批次之间增加更长的延迟
                    if (pagesProcessed < totalPages) {
                        const batchDelay = 300000 + Math.random() * 120000; // 5-7分钟
                        Utils.log('INFO', `批次处理完成，休息${Math.round(batchDelay/1000/60)}分钟后处理下一批次`);
                        
                        // 在长时间等待期间模拟用户行为
                        for (let i = 0; i < 3; i++) {
                            setTimeout(() => this.simulateUserBehavior(), i * 60000);
                        }
                        
                        await Utils.delay(batchDelay);
                    }
                }
                
                const finalCount = allQualifications.length;
                Utils.log('INFO', `资质数据获取完成，实际获取${finalCount}条（预期${totalCount}条）`);
                
                if (finalCount < totalCount) {
                    Utils.log('WARN', `由于频率限制或其他原因，未获取完全部数据。已获取：${finalCount}/${totalCount}条`);
                    // 显示友好提示
                    GM_notification(`数据获取完成`, `已成功获取${finalCount}条资质数据，占总数据的${Math.round(finalCount/totalCount*100)}%`, 'info');
                } else {
                    GM_notification('数据获取成功', `已完整获取全部${finalCount}条资质数据`, 'success');
                }
                
                return allQualifications.map(item => this.transformQualificationData(item));
                
            } catch (error) {
                Utils.log('ERROR', '获取所有资质数据失败', error.message);
                throw error;
            }
        },
        
        // 发送HTTP请求
        makeRequest: function(url, options) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: options.method || 'POST',
                    url: url,
                    headers: options.headers || {},
                    data: options.body || '',
                    timeout: 10000,
                    onload: function(response) {
                        try {
                            const data = JSON.parse(response.responseText);
                            resolve(data);
                        } catch (error) {
                            reject(new Error('响应数据解析失败'));
                        }
                    },
                    onerror: function(error) {
                        reject(new Error('网络请求失败'));
                    },
                    ontimeout: function() {
                        reject(new Error('请求超时'));
                    }
                });
            });
        },
        
        // 获取通用请求头
        getCommonHeaders: function() {
            return {
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "content-type": "application/json;charset=UTF-8",
                "dsm-client-info": '{"terminal":"0"}',
                "dsm-lang": "zh-CN",
                "dsm-platform": "pc",
                "dsm-site": "",
                "priority": "u=1, i",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "x-requested-with": "XMLHttpRequest"
            };
        },
        
        // 获取资质接口专用请求头
        getQualificationHeaders: function() {
            const headers = this.getCommonHeaders();
            headers["dsm-file-path"] = "lineation-price";
            return headers;
        },
        
        // 转换账户数据格式
        transformAccountData: function(data) {
            // 将所有扩展数据合并到一个对象中
            const extendedData = {
                currentBelongType: data.currentBelongType || '',
                belongVos: data.belongVos || [],
                isMainUser: data.mainUser || false,
                canSwitchIdentity: data.switchIdentity || false,
                businessFieldName: data.belongVos?.[0]?.businessFieldName || '',
                businessFieldCode: data.belongVos?.[0]?.businessFieldCode || '',
                scrapedAt: new Date().toISOString(),
                rawData: data // 原始数据
            };

            return {
                accountCode: data.venderId || '',
                accountName: data.pin || '',
                managerName: CONFIG.MANAGER_NAME || '', // 添加管理员姓名
                extendedData: JSON.stringify(extendedData) // 所有扩展数据作为JSON字符串
            };
        },
        
        // 转换资质数据格式
        transformQualificationData: function(item) {
            // 安全字符串截取函数，确保不超出数据库字段限制
            const safeTruncate = (str, maxLength) => {
                if (!str) return '';
                return str.length > maxLength ? str.substring(0, maxLength - 3) + '...' : str;
            };
            
            return {
                // vcAccount字段现在应该存储account_name，而不是account_code
                // 后台会根据这个值查找对应的vc_account记录并设置vcAccountId
                vcAccount: safeTruncate(item.vendorCode || '', 50), // 暂时使用vendorCode，后台会处理映射关系
                brand: safeTruncate(item.brandName || '', 100),
                brandCode: safeTruncate(item.brandCode || '', 50),
                // 将品牌等级信息映射到产品线分级字段
                productLineLevel: safeTruncate(item.brandGradeName || '', 100),
                firstCategory: safeTruncate(item.brandSort1Name || '', 100),
                firstCategoryCode: safeTruncate(item.brandSort1Code || '', 50),
                secondCategory: safeTruncate(item.brandSort2Name || '', 100),
                secondCategoryCode: safeTruncate(item.brandSort2Code || '', 50),
                thirdCategory: safeTruncate(item.brandSort3Name || '', 100),
                thirdCategoryCode: safeTruncate(item.brandSort3Code || '', 50),
                qualificationExpireDate: item.expiryDate ? Utils.formatDate(item.expiryDate) : '',
                purchaser: safeTruncate(item.purchaserCode || '', 50),
                purchaserName: safeTruncate(item.purchaserName || '', 100),
                // 将部门信息映射到一级部门字段
                firstDepartment: safeTruncate(item.deptName || '', 100),
                firstDepartmentCode: safeTruncate(item.deptCode || '', 50),
                // 使用品牌类型描述作为产品线类型
                productLineType: safeTruncate(item.brandTypeDesc || '', 100),
                status: item.status || 0,
                scraped_at: new Date().toISOString()
                // 完全移除remark字段 - v1.0.1
            };
        },
        
        // 模拟真实用户行为以避免风控检测
        simulateUserBehavior: function() {
            try {
                // 随机滚动页面
                const scrollAmount = Math.random() * 300 + 100;
                window.scrollBy(0, scrollAmount);
                
                // 随机等待一小段时间后滚回
                setTimeout(() => {
                    window.scrollBy(0, -scrollAmount/2);
                }, Math.random() * 2000 + 1000);
                
                // 模拟鼠标移动
                const mouseEvent = new MouseEvent('mousemove', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight
                });
                document.dispatchEvent(mouseEvent);
                
                Utils.log('INFO', '模拟用户行为：滚动页面和鼠标移动');
            } catch (error) {
                Utils.log('WARN', '模拟用户行为失败', error.message);
            }
        },

        // 扩展用户行为模拟 - 用于长时间等待期间
        simulateExtendedUserBehavior: function() {
            try {
                Utils.log('INFO', '开始扩展用户行为模拟，降低风控风险');
                
                // 模拟用户在多个时间点的活动
                const intervals = [5000, 15000, 30000, 60000, 120000]; // 5秒、15秒、30秒、1分钟、2分钟
                
                intervals.forEach((delay, index) => {
                    setTimeout(() => {
                        // 模拟随机页面活动
                        const actions = [
                            () => {
                                // 模拟滚动
                                const scroll = Math.random() * 500 + 200;
                                window.scrollBy(0, Math.random() > 0.5 ? scroll : -scroll);
                            },
                            () => {
                                // 模拟点击（不实际点击，只触发鼠标事件）
                                const clickEvent = new MouseEvent('click', {
                                    clientX: Math.random() * window.innerWidth,
                                    clientY: Math.random() * window.innerHeight
                                });
                                document.dispatchEvent(clickEvent);
                            },
                            () => {
                                // 模拟键盘活动
                                const keyEvent = new KeyboardEvent('keydown', {
                                    key: ['Tab', 'Enter', 'Space'][Math.floor(Math.random() * 3)]
                                });
                                document.dispatchEvent(keyEvent);
                            }
                        ];
                        
                        // 随机选择一个动作执行
                        const action = actions[Math.floor(Math.random() * actions.length)];
                        action();
                        
                        Utils.log('INFO', `执行第${index + 1}次扩展行为模拟`);
                    }, delay);
                });
                
            } catch (error) {
                Utils.log('WARN', '扩展用户行为模拟失败', error.message);
            }
        }
    };
    
    // ===== API客户端模块 =====
    const ApiClient = {
        // 上传VC账户数据
        uploadAccountData: async function(accountData) {
            Utils.log('INFO', '开始上传VC账户数据');

            try {
                const response = await this.makeApiRequest('/vc-account', {
                    method: 'POST',
                    body: JSON.stringify(accountData)
                });

                // 保存同步结果，包含VC账号ID信息
                if (response && response.data) {
                    const syncResult = {
                        success: response.data.success,
                        entityId: response.data.entityId,
                        entityCode: response.data.entityCode,
                        accountCode: accountData.accountCode,
                        accountName: accountData.accountName,
                        syncTime: new Date().toISOString()
                    };

                    // 保存到本地存储，供后续资质同步使用
                    GM_setValue('vc_account_sync_result', syncResult);
                    Utils.log('INFO', 'VC账户同步结果已保存', syncResult);
                }

                Utils.log('INFO', 'VC账户数据上传成功', response);
                return response;
            } catch (error) {
                Utils.log('ERROR', 'VC账户数据上传失败', error.message);
                throw error;
            }
        },

        // 获取已同步的VC账号信息
        getSyncedVcAccountInfo: function() {
            const syncResult = GM_getValue('vc_account_sync_result', null);
            if (syncResult && syncResult.success && syncResult.entityId) {
                return {
                    vcAccountId: syncResult.entityId,
                    vcAccountCode: syncResult.entityCode,
                    accountName: syncResult.accountName,
                    syncTime: syncResult.syncTime
                };
            }
            return null;
        },

        // 批量上传资质数据
        batchUploadQualifications: async function(qualifications) {
            Utils.log('INFO', `开始批量上传资质数据，共${qualifications.length}条`);
            
            try {
                // 一次性发送所有数据
                const response = await this.makeApiRequest('/qualifications/batch', {
                    method: 'POST',
                    body: JSON.stringify({ 
                        qualifications: qualifications,  // 发送所有数据
                        syncMode: 2,  // 覆盖更新模式
                        dataSource: 'TAMPERMONKEY_SCRIPT'
                    })
                });
                
                Utils.log('INFO', `批量上传成功，响应:`, response);
                
                // 更新进度到100%
                UI.updateUploadProgress(qualifications.length, qualifications.length);
                
                // 返回上传成功的数量
                Utils.log('INFO', `API响应原始数据:`, response);
                
                // 即使部分失败，也要返回成功的数量
                const successCount = response && response.data && typeof response.data.successCount === 'number' 
                    ? response.data.successCount 
                    : 0; // 如果获取不到数量，返回0而不是发送数量
                
                Utils.log('INFO', `批量上传完成，成功数量: ${successCount}, 响应中的successCount: ${response?.data?.successCount}`);
                
                // 如果有错误信息，记录但不抛出异常
                if (response?.data?.errorMessage) {
                    Utils.log('WARN', `上传过程中有错误: ${response.data.errorMessage}`);
                }
                
                return successCount;
                
            } catch (error) {
                Utils.log('ERROR', `批量上传失败`, error.message);
                throw error;
            }
        },
        
        // 发送API请求
        makeApiRequest: function(endpoint, options = {}) {
            const url = CONFIG.API_BASE_URL + endpoint;
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.API_TOKEN}`,
                ...options.headers
            };
            
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: options.method || 'GET',
                    url: url,
                    headers: headers,
                    data: options.body || '',
                    timeout: 30000,
                    onload: function(response) {
                        try {
                            if (response.status >= 200 && response.status < 300) {
                                const data = JSON.parse(response.responseText);
                                resolve(data);
                            } else {
                                reject(new Error(`API请求失败: ${response.status} ${response.statusText}`));
                            }
                        } catch (error) {
                            reject(new Error('API响应解析失败'));
                        }
                    },
                    onerror: function(error) {
                        reject(new Error('API请求网络错误'));
                    },
                    ontimeout: function() {
                        reject(new Error('API请求超时'));
                    }
                });
            });
        },

        // 测试API连通性
        testConnection: async function() {
            Utils.log('INFO', '开始测试API连通性');
            
            try {
                const response = await this.makeApiRequest('/test', {
                    method: 'GET'
                });
                
                Utils.log('INFO', 'API连通性测试成功', response);
                return response;
            } catch (error) {
                Utils.log('ERROR', 'API连通性测试失败', error.message);
                throw error;
            }
        }
    };
    
    // ===== 用户界面模块 =====
    const UI = {
        panel: null,
        
        // 初始化界面
        init: function() {
            this.createPanel();
            this.bindEvents();
            this.updateStatistics(); // 添加统计信息更新
            this.updateSyncedVcInfo(); // 更新已同步VC账号信息显示
            Utils.log('INFO', '用户界面初始化完成');
        },
        
        // 创建操作面板
        createPanel: function() {
            // 检查是否为首次使用
            const isFirstTime = !CONFIG.FIRST_SETUP_DONE;
            const defaultMinimized = !isFirstTime; // 首次之外默认最小化
            
            const panelHtml = `
                <div id="jingmai-scraper-panel" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 260px;
                    background: #fff;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 10000;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                ">
                    <div style="
                        background: #f5f5f5;
                        padding: 8px 12px;
                        border-bottom: 1px solid #ddd;
                        border-radius: 6px 6px 0 0;
                        cursor: move;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <span style="font-weight: bold; font-size: 12px;">京麦数据抓取</span>
                        <span id="minimize-btn" style="cursor: pointer; font-weight: bold; width: 16px; height: 16px; text-align: center; line-height: 16px; background: #007cff; color: white; border-radius: 2px; font-size: 10px;">${defaultMinimized ? '+' : '−'}</span>
                    </div>
                    <div id="panel-content" style="padding: 12px; ${defaultMinimized ? 'display: none;' : ''}">
                        <div style="margin-bottom: 12px;">
                            <div style="margin-bottom: 4px; font-size: 11px;"><strong>页面:</strong> <span id="page-type">检测中...</span></div>
                            <div style="margin-bottom: 4px; font-size: 11px;"><strong>状态:</strong> <span id="scraper-status">就绪</span></div>
                        </div>
                        
                        <div style="margin-bottom: 12px;">
                            <button id="scrape-account-btn" style="
                                width: 100%;
                                padding: 6px;
                                margin-bottom: 4px;
                                background: #007cff;
                                color: white;
                                border: none;
                                border-radius: 3px;
                                cursor: pointer;
                                font-size: 11px;
                            ">抓取VC账户</button>
                            
                            <button id="scrape-qualifications-btn" style="
                                width: 100%;
                                padding: 6px;
                                margin-bottom: 4px;
                                background: #28a745;
                                color: white;
                                border: none;
                                border-radius: 3px;
                                cursor: pointer;
                                font-size: 11px;
                            ">抓取资质数据</button>
                            
                            <button id="upload-data-btn" style="
                                width: 100%;
                                padding: 6px;
                                margin-bottom: 8px;
                                background: #ffc107;
                                color: #000;
                                border: none;
                                border-radius: 3px;
                                cursor: pointer;
                                font-size: 11px;
                            ">上传到系统</button>
                            
                            <div style="display: flex; gap: 4px;">
                                <button id="settings-btn" style="
                                    flex: 1;
                                    padding: 6px;
                                    background: #17a2b8;
                                    color: white;
                                    border: none;
                                    border-radius: 3px;
                                    cursor: pointer;
                                    font-size: 10px;
                                ">设置</button>
                                <button id="reset-btn" style="
                                    flex: 1;
                                    padding: 6px;
                                    background: #dc3545;
                                    color: white;
                                    border: none;
                                    border-radius: 3px;
                                    cursor: pointer;
                                    font-size: 10px;
                                ">重置</button>
                            </div>
                        </div>
                        
                        <div id="progress-section" style="margin-bottom: 12px; display: none;">
                            <div style="margin-bottom: 4px; font-size: 11px;"><strong>进度:</strong></div>
                            <div style="
                                width: 100%;
                                height: 16px;
                                background: #f0f0f0;
                                border-radius: 8px;
                                overflow: hidden;
                            ">
                                <div id="progress-bar" style="
                                    height: 100%;
                                    background: #007cff;
                                    width: 0%;
                                    transition: width 0.3s;
                                "></div>
                            </div>
                            <div id="progress-text" style="text-align: center; margin-top: 4px; font-size: 10px;">0%</div>
                        </div>
                        
                        <div style="font-size: 10px;">
                            <div style="margin-bottom: 3px; font-weight: bold; color: #666;">统计信息:</div>
                            <div style="color: #666;">
                                <div>VC账户: <span id="account-count">0</span></div>
                                <div>资质数据: <span id="qualification-count">0</span></div>
                                <div>上传成功: <span id="upload-success-count">0</span></div>
                                <div id="synced-vc-info" style="margin-top: 4px; color: #28a745; display: none; font-size: 9px;">
                                    VC账号: <span id="synced-vc-name"></span> (ID: <span id="synced-vc-id"></span>)
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(panelHtml);
            this.panel = $('#jingmai-scraper-panel');
            
            // 更新页面类型显示
            const pageType = Utils.getPageType();
            $('#page-type').text(pageType === 'account' ? 'VC账户' : 
                                pageType === 'qualification' ? '资质管理' : '未知');
        },
        
        // 绑定事件
        bindEvents: function() {
            // 最小化/展开面板
            $('#minimize-btn').click(() => {
                const content = $('#panel-content');
                const btn = $('#minimize-btn');
                if (content.is(':visible')) {
                    content.hide();
                    btn.text('+');
                } else {
                    content.show();
                    btn.text('−');
                }
            });
            
            // 抓取VC账户信息
            $('#scrape-account-btn').click(async () => {
                await this.handleScrapeAccount();
            });
            
            // 抓取资质数据
            $('#scrape-qualifications-btn').click(async () => {
                await this.handleScrapeQualifications();
            });
            
            // 上传数据
            $('#upload-data-btn').click(async () => {
                await this.handleUploadData();
            });
            
            // 设置按钮
            $('#settings-btn').click(() => {
                this.showSettingsDialog();
            });

            // 重置按钮
            $('#reset-btn').click(() => {
                Main.resetToFirstSetup();
            });
            
            // 拖拽功能
            this.makeDraggable();
        },
        
        // 处理抓取VC账户信息
        handleScrapeAccount: async function() {
            this.setStatus('正在抓取VC账户信息...');
            this.disableButtons();
            
            try {
                const accountData = await DataScraper.getAccountInfo();
                GM_setValue('scraped_account', accountData);
                
                this.updateStatistics(); // 更新统计信息
                this.setStatus('VC账户信息抓取完成');
                GM_notification('VC账户信息抓取成功', 'success');
                
                // 检查是否需要自动上传（非首次使用）
                if (CONFIG.FIRST_SETUP_DONE) {
                    Utils.log('INFO', '非首次使用，启动自动上传VC账户信息');
                    setTimeout(async () => {
                        await this.autoUploadAccount(accountData);
                    }, 2000); // 延迟2秒后开始自动上传
                }
                
            } catch (error) {
                this.setStatus('VC账户信息抓取失败');
                GM_notification('VC账户信息抓取失败: ' + error.message, 'error');
            } finally {
                this.enableButtons();
            }
        },

        // 自动上传VC账户信息（仅非首次使用时）
        autoUploadAccount: async function(accountData) {
            Utils.log('INFO', '开始自动上传VC账户信息');
            this.setStatus('自动上传VC账户信息...');
            this.disableButtons();
            
            try {
                const uploadResult = await ApiClient.uploadAccountData(accountData);
                
                if (uploadResult && uploadResult.data) {
                    const result = uploadResult.data;
                    if (result.success && result.entityId) {
                        Utils.log('INFO', `VC账户同步成功: ID=${result.entityId}`);
                        this.setStatus(`VC账户自动上传完成 (ID: ${result.entityId})`);

                        // 更新已同步VC账号信息显示
                        this.updateSyncedVcInfo();

                        // 显示简单完成通知
                        this.showSimpleNotification('VC账户信息已自动同步到系统', 'success');
                        
                        // 保存VC账户上传成功的日期
                        GM_setValue('last_account_upload_time', new Date().toDateString());
                    }
                }
                
                this.updateStatistics();
                
            } catch (error) {
                this.setStatus('VC账户自动上传失败');
                Utils.log('ERROR', 'VC账户自动上传失败', error.message);
                GM_notification('VC账户自动上传失败: ' + error.message, 'error');
            } finally {
                this.enableButtons();
            }
        },

        // 显示简单通知（用于VC账户上传完成）
        showSimpleNotification: function(message, type = 'success') {
            const bgColor = type === 'success' ? '#28a745' : '#dc3545';
            const icon = type === 'success' ? '✓' : '✕';
            
            const notificationHtml = `
                <div id="simple-notification" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 300px;
                    background: ${bgColor};
                    color: white;
                    border-radius: 8px;
                    padding: 15px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                    z-index: 10002;
                    font-family: Arial, sans-serif;
                    display: flex;
                    align-items: center;
                    animation: slideIn 0.3s ease-out;
                ">
                    <div style="
                        width: 24px;
                        height: 24px;
                        background: rgba(255,255,255,0.2);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 12px;
                        font-weight: bold;
                    ">${icon}</div>
                    <div style="flex: 1; font-size: 14px;">${message}</div>
                    <span id="simple-notification-close" style="
                        cursor: pointer;
                        font-weight: bold;
                        margin-left: 8px;
                        opacity: 0.7;
                    ">×</span>
                </div>
                <style>
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                </style>
            `;

            // 移除之前的通知
            $('#simple-notification').remove();
            $('body').append(notificationHtml);

            // 绑定关闭事件
            $('#simple-notification-close').click(() => {
                $('#simple-notification').remove();
            });

            // 3秒后自动消失
            setTimeout(() => {
                $('#simple-notification').remove();
            }, 3000);
        },
        
        // 处理抓取资质数据
        handleScrapeQualifications: async function() {
            this.setStatus('正在抓取资质数据...');
            this.disableButtons();
            this.showProgress();
            
            try {
                const qualifications = await DataScraper.getAllQualifications();
                GM_setValue('scraped_qualifications', qualifications);
                
                this.updateStatistics(); // 更新统计信息
                this.setStatus(`资质数据抓取完成，共${qualifications.length}条`);
                
                // 检查是否需要自动上传（非首次使用）
                if (CONFIG.FIRST_SETUP_DONE) {
                    Utils.log('INFO', '非首次使用，启动自动上传资质数据');
                    setTimeout(async () => {
                        await this.autoUploadAfterScrape(qualifications);
                    }, 2000); // 延迟2秒后开始自动上传
                } else {
                    GM_notification('资质数据抓取成功', `已获取${qualifications.length}条数据，请点击上传按钮同步到系统`, 'success');
                }
                
            } catch (error) {
                if (error.message.includes('频率限制') || error.message.includes('请求被限制')) {
                    this.setStatus('请求被限制，请稍后重试');
                    this.showFrequencyLimitNotification();
                } else {
                    this.setStatus('资质数据抓取失败');
                    GM_notification('资质数据抓取失败: ' + error.message, 'error');
                }
            } finally {
                this.enableButtons();
                this.hideProgress();
            }
        },

        // 显示频率限制友好提示
        showFrequencyLimitNotification: function() {
            // 检查是否已经打开了频率限制提示
            if ($('#frequency-limit-notification').length > 0) {
                return;
            }
            
            const notificationHtml = `
                <div id="frequency-limit-notification" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 450px;
                    background: white;
                    border: 2px solid #ff9800;
                    border-radius: 12px;
                    padding: 25px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                    text-align: center;
                ">
                    <div style="position: relative; margin-bottom: 20px;">
                        <div style="
                            width: 60px;
                            height: 60px;
                            background: #ff9800;
                            border-radius: 50%;
                            margin: 0 auto 15px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 30px;
                            color: white;
                        ">⚠</div>
                        <h3 style="margin: 0; color: #ff9800; font-size: 20px;">请求频率限制</h3>
                        <span id="frequency-notification-close" style="
                            position: absolute;
                            top: -10px;
                            right: -10px;
                            width: 25px;
                            height: 25px;
                            background: #dc3545;
                            color: white;
                            border-radius: 50%;
                            text-align: center;
                            line-height: 25px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 16px;
                        ">×</span>
                    </div>

                    <div style="margin-bottom: 20px; color: #666; line-height: 1.6;">
                        <p style="margin-bottom: 15px;">京麦服务器检测到请求过于频繁，暂时限制了访问。</p>
                        
                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <div style="font-weight: bold; color: #856404; margin-bottom: 8px;">建议解决方案：</div>
                            <div style="text-align: left; color: #856404; font-size: 14px;">
                                • 等待 <strong>5-10 分钟</strong> 后重试<br>
                                • 避免在短时间内多次点击抓取按钮<br>
                                • 如有部分数据已获取，可先上传已有数据<br>
                                • 可尝试刷新页面后重新开始
                            </div>
                        </div>
                        
                        <div style="font-size: 12px; color: #999;">
                            脚本已自动增加延迟和重试机制，但京麦服务器仍有严格的频率限制
                        </div>
                    </div>

                    <div>
                        <button id="frequency-notification-ok" style="
                            padding: 10px 20px;
                            background: #ff9800;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                            margin-right: 10px;
                        ">知道了</button>
                        <button id="frequency-check-data" style="
                            padding: 10px 20px;
                            background: #28a745;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                        ">查看已获取数据</button>
                    </div>
                </div>
            `;

            $('body').append(notificationHtml);

            // 绑定事件
            $('#frequency-notification-close, #frequency-notification-ok').click(() => {
                this.closeFrequencyLimitNotification();
            });
            
            $('#frequency-check-data').click(() => {
                this.closeFrequencyLimitNotification();
                this.updateStatistics(); // 更新统计显示
                this.showSimpleNotification('已刷新数据统计，请查看面板中的统计信息', 'info');
            });
        },

        // 关闭频率限制通知
        closeFrequencyLimitNotification: function() {
            $('#frequency-limit-notification').remove();
        },

        // 自动上传抓取的数据（仅非首次使用时）
        autoUploadAfterScrape: async function(qualifications) {
            Utils.log('INFO', '开始自动上传抓取的数据');
            this.setStatus('自动上传数据中...');
            this.disableButtons();
            this.showProgress();
            
            try {
                let uploadCount = 0;
                
                // 上传VC账户数据
                const accountData = GM_getValue('scraped_account', null);
                if (accountData) {
                    const uploadResult = await ApiClient.uploadAccountData(accountData);
                    uploadCount++;

                    if (uploadResult && uploadResult.data) {
                        const result = uploadResult.data;
                        if (result.success && result.entityId) {
                            Utils.log('INFO', `VC账户同步成功: ID=${result.entityId}`);
                            // 更新已同步VC账号信息显示
                            this.updateSyncedVcInfo();
                            // 保存VC账户上传成功的日期
                            GM_setValue('last_account_upload_time', new Date().toDateString());
                        }
                    }
                }
                
                // 上传资质数据
                if (qualifications.length > 0) {
                    try {
                        const qualificationSuccessCount = await ApiClient.batchUploadQualifications(qualifications);
                        Utils.log('INFO', `资质上传返回的成功数量: ${qualificationSuccessCount}`);
                        
                        if (qualificationSuccessCount !== null && qualificationSuccessCount !== undefined && !isNaN(qualificationSuccessCount)) {
                            uploadCount += qualificationSuccessCount;
                        } else {
                            Utils.log('WARN', `资质上传返回无效数量: ${qualificationSuccessCount}, 使用默认值: ${qualifications.length}`);
                            uploadCount += qualifications.length; // 使用发送数量作为后备
                        }
                    } catch (error) {
                        Utils.log('ERROR', '资质数据上传失败', error.message);
                        // 上传失败但不中断流程，使用0作为上传成功数
                        uploadCount += 0;
                    }
                }
                
                this.updateCount('upload-success-count', uploadCount);
                this.updateStatistics();
                this.setStatus(`自动上传完成，成功${uploadCount}条`);
                
                // 保存上传成功数量到存储
                GM_setValue('upload_success_count', uploadCount);
                
                // 保存上传成功的日期
                GM_setValue('last_upload_time', new Date().toDateString());
                
                // 显示完成通知页面
                this.showCompletionNotification(uploadCount, qualifications.length);
                
                Utils.log('INFO', `自动上传完成: 成功${uploadCount}条`);
                
            } catch (error) {
                this.setStatus('自动上传失败');
                Utils.log('ERROR', '自动上传失败', error.message);
                GM_notification('自动上传失败: ' + error.message, 'error');
            } finally {
                this.enableButtons();
                this.hideProgress();
            }
        },

        // 显示完成通知页面
        showCompletionNotification: function(uploadCount, scrapedCount) {
            // 检查是否已经打开了通知对话框，避免重复打开
            if ($('#completion-notification').length > 0) {
                return;
            }
            
            const notificationHtml = `
                <div id="completion-notification" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 400px;
                    background: white;
                    border: 2px solid #28a745;
                    border-radius: 12px;
                    padding: 25px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                    text-align: center;
                ">
                    <div style="position: relative; margin-bottom: 20px;">
                        <div style="
                            width: 60px;
                            height: 60px;
                            background: #28a745;
                            border-radius: 50%;
                            margin: 0 auto 15px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 30px;
                            color: white;
                        ">✓</div>
                        <h3 style="margin: 0; color: #28a745; font-size: 20px;">数据同步完成</h3>
                        <span id="notification-close-btn" style="
                            position: absolute;
                            top: -10px;
                            right: -10px;
                            width: 25px;
                            height: 25px;
                            background: #dc3545;
                            color: white;
                            border-radius: 50%;
                            text-align: center;
                            line-height: 25px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 16px;
                        ">×</span>
                    </div>

                    <div style="margin-bottom: 20px; color: #666;">
                        <div style="margin-bottom: 8px; font-size: 14px;">
                            <strong>抓取数据：</strong> ${scrapedCount} 条资质记录
                        </div>
                        <div style="margin-bottom: 8px; font-size: 14px;">
                            <strong>上传成功：</strong> ${uploadCount} 条记录
                        </div>
                        <div style="font-size: 12px; color: #999;">
                            同步时间：${new Date().toLocaleString()}
                        </div>
                    </div>

                    <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 12px; color: #666;">
                        <div style="margin-bottom: 5px;"><strong>完成操作：</strong></div>
                        <div style="text-align: left;">
                            • 已抓取京麦资质数据并保存到本地<br>
                            • 已自动同步数据到资质管理系统<br>
                            • 数据已按照VC账号+品牌+分类去重处理<br>
                            • 下次访问将跳过重复数据的抓取
                        </div>
                    </div>

                    <div>
                        <button id="notification-ok" style="
                            padding: 10px 20px;
                            background: #28a745;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                            margin-right: 10px;
                        ">确定</button>
                        <button id="notification-view-logs" style="
                            padding: 10px 20px;
                            background: #17a2b8;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                        ">查看日志</button>
                    </div>
                </div>
            `;

            $('body').append(notificationHtml);

            // 绑定事件
            $('#notification-close-btn, #notification-ok').click(() => {
                this.closeCompletionNotification();
            });
            
            $('#notification-view-logs').click(() => {
                this.closeCompletionNotification();
                this.showLogsDialog();
            });

            // 播放通知声音（如果浏览器支持）
            try {
                GM_notification('京麦数据同步完成', `成功同步${uploadCount}条记录到系统`, 'success');
            } catch (e) {
                // 静默处理通知错误
            }
        },

        // 关闭完成通知
        closeCompletionNotification: function() {
            $('#completion-notification').remove();
        },
        
        // 处理数据上传
        handleUploadData: async function() {
            this.setStatus('正在上传数据...');
            this.disableButtons();
            this.showProgress();
            
            try {
                let uploadCount = 0;
                
                // 上传VC账户数据
                const accountData = GM_getValue('scraped_account', null);
                if (accountData) {
                    const uploadResult = await ApiClient.uploadAccountData(accountData);
                    uploadCount++;

                    // 显示详细的上传结果
                    if (uploadResult && uploadResult.data) {
                        const result = uploadResult.data;
                        if (result.success && result.entityId) {
                            Utils.log('INFO', `VC账户同步成功: ID=${result.entityId}, Code=${result.entityCode}`);
                            this.setStatus(`VC账户同步成功 (ID: ${result.entityId})`);
                            // 更新已同步VC账号信息显示
                            this.updateSyncedVcInfo();
                            // 保存VC账户上传成功的日期
                            GM_setValue('last_account_upload_time', new Date().toDateString());
                        }
                    }
                }
                
                // 启用资质数据上传，上传所有数据
                const qualifications = GM_getValue('scraped_qualifications', []);
                if (qualifications.length > 0) {
                    const qualificationSuccessCount = await ApiClient.batchUploadQualifications(qualifications);
                    uploadCount += qualificationSuccessCount;
                }
                
                this.updateCount('upload-success-count', uploadCount);
                this.updateStatistics(); // 更新统计信息
                this.setStatus(`数据上传完成，成功${uploadCount}条`);
                
                // 保存上传成功数量到存储
                GM_setValue('upload_success_count', uploadCount);
                
                // 保存上传成功的日期  
                GM_setValue('last_upload_time', new Date().toDateString());
                
                GM_notification(`数据上传成功，共${uploadCount}条`, 'success');
                
            } catch (error) {
                this.setStatus('数据上传失败');
                GM_notification('数据上传失败: ' + error.message, 'error');
            } finally {
                this.enableButtons();
                this.hideProgress();
            }
        },

        // 显示设置对话框
        showSettingsDialog: function() {
            // 检查是否已经打开了设置对话框，避免重复打开
            if ($('#settings-dialog').length > 0) {
                return;
            }
            
            const settingsHtml = `
                <div id="settings-dialog" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 450px;
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                ">
                    <div style="position: relative; margin-bottom: 15px;">
                        <h3 style="margin: 0; color: #333;">京麦数据同步 - 设置</h3>
                        <span id="settings-close-btn" style="
                            position: absolute;
                            top: -5px;
                            right: -5px;
                            width: 25px;
                            height: 25px;
                            background: #dc3545;
                            color: white;
                            border-radius: 50%;
                            text-align: center;
                            line-height: 25px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 16px;
                        ">×</span>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #888;">API服务器地址: (不可修改)</label>
                        <input id="settings-api-url" type="text" value="${CONFIG.API_BASE_URL}" readonly disabled style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                            background-color: #f5f5f5;
                            color: #888;
                            cursor: not-allowed;
                        ">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">管理员姓名:</label>
                        <input id="settings-manager-name" type="text" value="${CONFIG.MANAGER_NAME}" placeholder="请输入管理员姓名" style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        ">
                        <small style="color: #666; margin-top: 5px; display: block;">
                            此姓名将用于匹配系统中的用户，作为VC账户的负责人
                        </small>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: flex; align-items: center;">
                            <input id="settings-auto-sync" type="checkbox" ${CONFIG.AUTO_SCRAPE ? 'checked' : ''} style="margin-right: 8px;">
                            <span>启用自动同步</span>
                        </label>
                        <small style="color: #666; margin-left: 20px;">
                            开启后，访问京麦页面时会自动同步数据到系统
                        </small>
                    </div>

                    <div style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">当前状态:</div>
                        <div style="font-size: 12px;">
                            <div>✓ 首次设置: ${CONFIG.FIRST_SETUP_DONE ? '已完成' : '未完成'}</div>
                            <div>✓ API地址: ${CONFIG.API_BASE_URL || '未设置'}</div>
                            <div>✓ 管理员: ${CONFIG.MANAGER_NAME || '未设置'}</div>
                            <div>✓ 自动同步: ${CONFIG.AUTO_SCRAPE ? '已启用' : '已禁用'}</div>
                        </div>
                    </div>

                    <div style="display: flex; flex-wrap: wrap; justify-content: flex-end; gap: 8px; align-items: center;">
                        <button id="settings-test-connection" style="
                            padding: 6px 12px;
                            background: #28a745;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">测试连接</button>
                        <button id="settings-test-manager" style="
                            padding: 6px 12px;
                            background: #17a2b8;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">测试管理员</button>
                        <button id="settings-logs" style="
                            padding: 6px 12px;
                            background: #ffc107;
                            color: #000;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">查看日志</button>
                        <button id="settings-cancel" style="
                            padding: 6px 12px;
                            background: #6c757d;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">取消</button>
                        <button id="settings-save" style="
                            padding: 6px 12px;
                            background: #007cff;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">保存</button>
                    </div>

                    <div id="settings-status" style="
                        margin-top: 15px;
                        padding: 8px;
                        border-radius: 4px;
                        display: none;
                    "></div>
                </div>
            `;

            $('body').append(settingsHtml);

            // 绑定事件
            $('#settings-close-btn').click(() => this.closeSettingsDialog());
            $('#settings-test-connection').click(() => this.testConnectionInSettings());
            $('#settings-test-manager').click(() => this.testManagerInSettings());
            $('#settings-logs').click(() => this.showLogsDialog());
            $('#settings-cancel').click(() => this.closeSettingsDialog());
            $('#settings-save').click(() => this.saveSettings());
        },

        // 在设置中测试连接
        testConnectionInSettings: function() {
            $('#settings-status').show().html('正在测试连接...').css({
                'background': '#e3f2fd',
                'color': '#1976d2',
                'border': '1px solid #bbdefb'
            });

            GM_xmlhttpRequest({
                method: 'GET',
                url: CONFIG.API_BASE_URL + '/test',
                timeout: 5000,
                onload: function(response) {
                    if (response.status === 200) {
                        $('#settings-status').html('✅ 连接成功！服务器响应正常').css({
                            'background': '#e8f5e8',
                            'color': '#2e7d32',
                            'border': '1px solid #c8e6c9'
                        });
                    } else {
                        $('#settings-status').html('❌ 连接失败：服务器响应异常').css({
                            'background': '#ffebee',
                            'color': '#c62828',
                            'border': '1px solid #ffcdd2'
                        });
                    }
                },
                onerror: function() {
                    $('#settings-status').html('❌ 连接失败：无法连接到服务器').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                },
                ontimeout: function() {
                    $('#settings-status').html('❌ 连接超时：请检查服务器地址').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                }
            });
        },

        // 在设置中测试管理员
        testManagerInSettings: function() {
            const managerName = $('#settings-manager-name').val().trim();
            if (!managerName) {
                $('#settings-status').show().html('❌ 请先输入管理员姓名').css({
                    'background': '#ffebee',
                    'color': '#c62828',
                    'border': '1px solid #ffcdd2'
                });
                return;
            }

            $('#settings-status').show().html('正在测试管理员匹配...').css({
                'background': '#e3f2fd',
                'color': '#1976d2',
                'border': '1px solid #bbdefb'
            });

            GM_xmlhttpRequest({
                method: 'GET',
                url: CONFIG.API_BASE_URL + '/test-manager?managerName=' + encodeURIComponent(managerName),
                timeout: 5000,
                onload: function(response) {
                    if (response.status === 200) {
                        try {
                            const result = JSON.parse(response.responseText);
                            if (result.code === 0 && result.data) {
                                $('#settings-status').html(`✅ 管理员匹配成功！用户ID: ${result.data.userId}, 姓名: ${result.data.nickname}`).css({
                                    'background': '#e8f5e8',
                                    'color': '#2e7d32',
                                    'border': '1px solid #c8e6c9'
                                });
                            } else {
                                $('#settings-status').html('❌ 未找到匹配的管理员，请检查姓名是否正确').css({
                                    'background': '#fff3e0',
                                    'color': '#f57c00',
                                    'border': '1px solid #ffcc02'
                                });
                            }
                        } catch (e) {
                            $('#settings-status').html('❌ 响应数据解析失败').css({
                                'background': '#ffebee',
                                'color': '#c62828',
                                'border': '1px solid #ffcdd2'
                            });
                        }
                    } else {
                        $('#settings-status').html('❌ 管理员匹配测试失败：服务器响应异常').css({
                            'background': '#ffebee',
                            'color': '#c62828',
                            'border': '1px solid #ffcdd2'
                        });
                    }
                },
                onerror: function() {
                    $('#settings-status').html('❌ 管理员匹配测试失败：无法连接到服务器').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                },
                ontimeout: function() {
                    $('#settings-status').html('❌ 管理员匹配测试超时：请检查服务器地址').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                }
            });
        },

        // 保存设置
        saveSettings: function() {
            // API地址不允许修改，所以不保存
            const managerName = $('#settings-manager-name').val().trim();
            const autoSync = $('#settings-auto-sync').is(':checked');

            // 保存配置（不保存API_BASE_URL，因为它是统一管理的）
            GM_setValue('MANAGER_NAME', managerName);
            GM_setValue('AUTO_SCRAPE', autoSync);

            // 更新当前配置（API地址保持不变）
            CONFIG.MANAGER_NAME = managerName;
            CONFIG.AUTO_SCRAPE = autoSync;

            // 关闭对话框
            this.closeSettingsDialog();

            // 显示成功消息
            GM_notification('设置已保存', '配置更新成功', 'success');
        },

        // 关闭设置对话框
        closeSettingsDialog: function() {
            $('#settings-dialog').remove();
        },

        // 显示日志对话框
        showLogsDialog: function() {
            const logs = GM_getValue('scrape_logs', []);
            const logsHtml = logs.slice(-50).reverse().map(log => 
                `<div style="margin-bottom: 8px; padding: 6px; background: ${
                    log.level === 'ERROR' ? '#fff5f5' : 
                    log.level === 'WARN' ? '#fffbf0' : '#f8f9fa'
                }; border-left: 3px solid ${
                    log.level === 'ERROR' ? '#dc3545' : 
                    log.level === 'WARN' ? '#ffc107' : '#007cff'
                }; font-size: 11px;">
                    <div style="font-weight: bold; color: #666;">[${log.timestamp.split('T')[1].split('.')[0]}] ${log.level}</div>
                    <div style="margin-top: 2px;">${log.message}</div>
                    ${log.data ? `<div style="margin-top: 2px; color: #666; font-family: monospace;">${JSON.stringify(log.data, null, 2).substring(0, 200)}...</div>` : ''}
                </div>`
            ).join('');
            
            const dialogHtml = `
                <div id="logs-dialog" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 600px;
                    max-width: 90vw;
                    height: 500px;
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                ">
                    <div style="padding: 15px; border-bottom: 1px solid #ddd;">
                        <strong>运行日志</strong>
                        <span id="close-logs" style="float: right; cursor: pointer; font-weight: bold;">×</span>
                        <button id="clear-logs" style="
                            float: right;
                            margin-right: 15px;
                            padding: 4px 8px;
                            background: #dc3545;
                            color: white;
                            border: none;
                            border-radius: 3px;
                            cursor: pointer;
                            font-size: 11px;
                        ">清空日志</button>
                    </div>
                    <div style="padding: 15px; height: 400px; overflow-y: auto;">
                        ${logsHtml || '<div style="text-align: center; color: #666; margin-top: 50px;">暂无日志</div>'}
                    </div>
                </div>
            `;
            
            $('body').append(dialogHtml);
            
            // 绑定事件
            $('#close-logs').click(() => {
                $('#logs-dialog').remove();
            });
            
            $('#clear-logs').click(() => {
                GM_setValue('scrape_logs', []);
                $('#logs-dialog').remove();
                GM_notification('日志已清空', 'success');
            });
        },
        
        // 工具方法
        setStatus: function(status) {
            $('#scraper-status').text(status);
        },

        // 更新已同步VC账号信息显示
        updateSyncedVcInfo: function() {
            const vcInfo = ApiClient.getSyncedVcAccountInfo();
            if (vcInfo) {
                $('#synced-vc-name').text(vcInfo.accountName);
                $('#synced-vc-id').text(vcInfo.vcAccountId);
                $('#synced-vc-info').show();
            } else {
                $('#synced-vc-info').hide();
            }
        },

        updateCount: function(elementId, count) {
            $(`#${elementId}`).text(count);
        },
        
        updateProgress: function(current, total) {
            const percent = Math.round((current / total) * 100);
            $('#progress-bar').css('width', percent + '%');
            $('#progress-text').text(`${percent}% (${current}/${total})`);
        },
        
        updateUploadProgress: function(current, total) {
            this.updateProgress(current, total);
        },
        
        showProgress: function() {
            $('#progress-section').show();
            this.updateProgress(0, 100);
        },
        
        hideProgress: function() {
            $('#progress-section').hide();
        },
        
        disableButtons: function() {
            $('#scrape-account-btn, #scrape-qualifications-btn, #upload-data-btn').prop('disabled', true);
        },
        
        enableButtons: function() {
            $('#scrape-account-btn, #scrape-qualifications-btn, #upload-data-btn').prop('disabled', false);
        },
        
        makeDraggable: function() {
            let isDragging = false;
            let startX, startY, startLeft, startTop;
            
            this.panel.find('div:first').on('mousedown', function(e) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                const offset = $('#jingmai-scraper-panel').offset();
                startLeft = offset.left;
                startTop = offset.top;
                
                $(document).on('mousemove', function(e) {
                    if (isDragging) {
                        const newLeft = startLeft + (e.clientX - startX);
                        const newTop = startTop + (e.clientY - startY);
                        $('#jingmai-scraper-panel').css({
                            left: newLeft + 'px',
                            top: newTop + 'px',
                            right: 'auto'
                        });
                    }
                });
                
                $(document).on('mouseup', function() {
                    isDragging = false;
                    $(document).off('mousemove mouseup');
                });
            });
        },
        
        // 更新统计信息
        updateStatistics: function() {
            // 更新VC账户统计
            const accountData = GM_getValue('scraped_account', null);
            const accountCount = accountData ? 1 : 0;
            this.updateCount('account-count', accountCount);
            
            // 更新资质数据统计
            const qualifications = GM_getValue('scraped_qualifications', []);
            const qualificationCount = Array.isArray(qualifications) ? qualifications.length : 0;
            this.updateCount('qualification-count', qualificationCount);
            
            // 显示已同步的VC账号信息
            const syncedVcInfo = ApiClient.getSyncedVcAccountInfo();
            if (syncedVcInfo) {
                $('#synced-vc-info').show();
                $('#synced-vc-name').text(syncedVcInfo.accountName || syncedVcInfo.vcAccountCode);
                $('#synced-vc-id').text(syncedVcInfo.vcAccountId);
            } else {
                $('#synced-vc-info').hide();
            }
            
            Utils.log('INFO', `统计信息更新: VC账户=${accountCount}, 资质数据=${qualificationCount}`);
        }
    };
    
    // ===== 主控制模块 =====
    const Main = {
        // 启动应用
        init: function() {
            Utils.log('INFO', '京麦数据抓取脚本开始初始化');

            // 等待页面加载完成
            $(document).ready(() => {
                // 初始化数据抓取模块
                DataScraper.init();
                
                // 检查是否完成初次设置
                if (!CONFIG.FIRST_SETUP_DONE) {
                    // 首次使用，显示设置界面
                    this.showFirstTimeSetup();
                } else {
                    // 已设置，初始化界面
                    UI.init();

                    // 自动开始同步（用户无感）
                    if (CONFIG.AUTO_SCRAPE) {
                        setTimeout(() => {
                            this.startAutoScrape();
                        }, 3000); // 延迟3秒开始，确保页面完全加载
                    }
                }

                Utils.log('INFO', '京麦数据抓取脚本初始化完成');
            });
        },

        // 显示首次设置界面
        showFirstTimeSetup: function() {
            const setupHtml = `
                <div id="first-setup-dialog" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 400px;
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: Arial, sans-serif;
                ">
                    <h3 style="margin: 0 0 15px 0; color: #333;">京麦数据同步 - 初次设置</h3>
                    <p style="color: #666; margin-bottom: 20px;">
                        欢迎使用京麦数据同步脚本！<br>
                        请设置管理员姓名并完成验证后，脚本将在您访问京麦页面时自动同步数据。
                    </p>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #888;">API服务器地址: (系统默认)</label>
                        <input id="setup-api-url" type="text" value="${CONFIG.API_BASE_URL}" readonly disabled style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                            background-color: #f5f5f5;
                            color: #888;
                            cursor: not-allowed;
                        ">
                        <small style="color: #999; margin-top: 2px; display: block;">
                            使用系统默认API地址，无需修改
                        </small>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #d32f2f;">管理员姓名: <span style="color: #d32f2f;">*</span></label>
                        <input id="setup-manager-name" type="text" value="${CONFIG.MANAGER_NAME}" placeholder="请输入管理员姓名（必填）" style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        ">
                        <small style="color: #666; margin-top: 5px; display: block;">
                            此姓名将用于匹配系统中的用户，作为VC账户的负责人
                        </small>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: flex; align-items: center;">
                            <input id="setup-auto-sync" type="checkbox" ${CONFIG.AUTO_SCRAPE ? 'checked' : ''} style="margin-right: 8px;">
                            <span>启用自动同步（推荐）</span>
                        </label>
                        <small style="color: #666; margin-left: 20px;">
                            开启后，访问京麦页面时会自动同步数据到系统
                        </small>
                    </div>

                    <div style="text-align: right;">
                        <button id="setup-test-manager" style="
                            padding: 8px 16px;
                            background: #17a2b8;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-right: 10px;
                        ">验证管理员</button>
                        <button id="setup-complete" style="
                            padding: 8px 16px;
                            background: #6c757d;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: not-allowed;
                            opacity: 0.6;
                        " disabled>完成设置</button>
                    </div>

                    <div id="setup-status" style="
                        margin-top: 15px;
                        padding: 8px;
                        border-radius: 4px;
                        display: none;
                    "></div>
                </div>
            `;

            $('body').append(setupHtml);

            // 绑定事件
            $('#setup-test-manager').click(() => this.testManagerMatch());
            $('#setup-complete').click(() => this.completeSetup());
            
            // 输入框变化时重新验证按钮状态
            $('#setup-manager-name').on('input', () => this.validateSetupForm());
            
            // 初始化时验证表单状态
            this.validateSetupForm();
        },

        // 验证设置表单状态
        validateSetupForm: function() {
            const managerName = $('#setup-manager-name').val().trim();
            const completeButton = $('#setup-complete');
            
            if (!managerName) {
                // 管理员姓名为空，禁用完成按钮
                completeButton.prop('disabled', true)
                    .css({
                        'background': '#6c757d',
                        'cursor': 'not-allowed',
                        'opacity': '0.6'
                    })
                    .text('完成设置');
                    
                // 清除之前的验证状态
                $('#setup-status').hide();
                GM_setValue('MANAGER_VERIFIED', false);
            } else {
                // 有输入但还未验证，显示为待验证状态
                const isVerified = GM_getValue('MANAGER_VERIFIED', false);
                const verifiedName = GM_getValue('VERIFIED_MANAGER_NAME', '');
                
                if (isVerified && verifiedName === managerName) {
                    // 已验证且姓名未改变
                    completeButton.prop('disabled', false)
                        .css({
                            'background': '#007cff',
                            'cursor': 'pointer',
                            'opacity': '1'
                        })
                        .text('完成设置');
                } else {
                    // 未验证或姓名已改变
                    completeButton.prop('disabled', true)
                        .css({
                            'background': '#ffc107',
                            'cursor': 'not-allowed',
                            'opacity': '0.8'
                        })
                        .text('请先验证管理员');
                        
                    // 如果姓名改变了，清除验证状态
                    if (verifiedName !== managerName) {
                        GM_setValue('MANAGER_VERIFIED', false);
                        $('#setup-status').hide();
                    }
                }
            }
        },

        // 测试连接
        testConnection: function() {
            $('#setup-status').show().html('正在测试连接...').css({
                'background': '#e3f2fd',
                'color': '#1976d2',
                'border': '1px solid #bbdefb'
            });

            // 测试API连接
            GM_xmlhttpRequest({
                method: 'GET',
                url: CONFIG.API_BASE_URL + '/test',
                timeout: 5000,
                onload: function(response) {
                    if (response.status === 200) {
                        $('#setup-status').html('✅ 连接成功！服务器响应正常').css({
                            'background': '#e8f5e8',
                            'color': '#2e7d32',
                            'border': '1px solid #c8e6c9'
                        });
                    } else {
                        $('#setup-status').html('❌ 连接失败：服务器响应异常').css({
                            'background': '#ffebee',
                            'color': '#c62828',
                            'border': '1px solid #ffcdd2'
                        });
                    }
                },
                onerror: function() {
                    $('#setup-status').html('❌ 连接失败：无法连接到服务器').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                },
                ontimeout: function() {
                    $('#setup-status').html('❌ 连接超时：请检查服务器地址').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                }
            });
        },

        // 测试管理员匹配
        testManagerMatch: function() {
            const managerName = $('#setup-manager-name').val().trim();
            if (!managerName) {
                $('#setup-status').show().html('❌ 请先输入管理员姓名').css({
                    'background': '#ffebee',
                    'color': '#c62828',
                    'border': '1px solid #ffcdd2'
                });
                return;
            }

            const apiUrl = $('#setup-api-url').val();
            $('#setup-status').show().html('正在验证管理员匹配...').css({
                'background': '#e3f2fd',
                'color': '#1976d2',
                'border': '1px solid #bbdefb'
            });

            // 测试管理员匹配
            GM_xmlhttpRequest({
                method: 'GET',
                url: CONFIG.API_BASE_URL + '/test-manager?managerName=' + encodeURIComponent(managerName),
                timeout: 5000,
                onload: function(response) {
                    if (response.status === 200) {
                        try {
                            const result = JSON.parse(response.responseText);
                            if (result.code === 0 && result.data) {
                                $('#setup-status').html(`✅ 管理员验证成功！用户ID: ${result.data.userId}, 姓名: ${result.data.nickname}`).css({
                                    'background': '#e8f5e8',
                                    'color': '#2e7d32',
                                    'border': '1px solid #c8e6c9'
                                });
                                
                                // 保存验证状态
                                GM_setValue('MANAGER_VERIFIED', true);
                                GM_setValue('VERIFIED_MANAGER_NAME', managerName);
                                
                                // 更新按钮状态
                                Main.validateSetupForm();
                            } else {
                                $('#setup-status').html('❌ 未找到匹配的管理员，请检查姓名是否正确').css({
                                    'background': '#fff3e0',
                                    'color': '#f57c00',
                                    'border': '1px solid #ffcc02'
                                });
                                
                                // 清除验证状态
                                GM_setValue('MANAGER_VERIFIED', false);
                                Main.validateSetupForm();
                            }
                        } catch (e) {
                            $('#setup-status').html('❌ 响应数据解析失败').css({
                                'background': '#ffebee',
                                'color': '#c62828',
                                'border': '1px solid #ffcdd2'
                            });
                            
                            GM_setValue('MANAGER_VERIFIED', false);
                            Main.validateSetupForm();
                        }
                    } else {
                        $('#setup-status').html('❌ 管理员验证失败：服务器响应异常').css({
                            'background': '#ffebee',
                            'color': '#c62828',
                            'border': '1px solid #ffcdd2'
                        });
                        
                        GM_setValue('MANAGER_VERIFIED', false);
                        Main.validateSetupForm();
                    }
                },
                onerror: function() {
                    $('#setup-status').html('❌ 管理员验证失败：无法连接到服务器').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                    
                    GM_setValue('MANAGER_VERIFIED', false);
                    Main.validateSetupForm();
                },
                ontimeout: function() {
                    $('#setup-status').html('❌ 管理员验证超时：请检查服务器地址').css({
                        'background': '#ffebee',
                        'color': '#c62828',
                        'border': '1px solid #ffcdd2'
                    });
                    
                    GM_setValue('MANAGER_VERIFIED', false);
                    Main.validateSetupForm();
                }
            });
        },

        // 完成设置
        completeSetup: function() {
            const managerName = $('#setup-manager-name').val().trim();
            const isVerified = GM_getValue('MANAGER_VERIFIED', false);
            const verifiedName = GM_getValue('VERIFIED_MANAGER_NAME', '');
            
            // 验证必填项和验证状态
            if (!managerName) {
                $('#setup-status').show().html('❌ 请输入管理员姓名').css({
                    'background': '#ffebee',
                    'color': '#c62828',
                    'border': '1px solid #ffcdd2'
                });
                return;
            }
            
            if (!isVerified || verifiedName !== managerName) {
                $('#setup-status').show().html('❌ 请先验证管理员姓名').css({
                    'background': '#fff3e0',
                    'color': '#f57c00',
                    'border': '1px solid #ffcc02'
                });
                return;
            }

            const autoSync = $('#setup-auto-sync').is(':checked');

            // 保存配置
            GM_setValue('AUTO_SCRAPE', autoSync);
            GM_setValue('MANAGER_NAME', managerName);
            GM_setValue('FIRST_SETUP_DONE', true);

            // 清除验证状态（已保存到配置中）
            GM_setValue('MANAGER_VERIFIED', false);
            GM_setValue('VERIFIED_MANAGER_NAME', '');

            // 更新当前配置
            CONFIG.AUTO_SCRAPE = autoSync;
            CONFIG.MANAGER_NAME = managerName;
            CONFIG.FIRST_SETUP_DONE = true;

            // 移除设置界面
            $('#first-setup-dialog').remove();

            // 显示成功消息
            GM_notification('设置完成', '京麦数据同步已配置完成，正在初始化...');

            // 立即初始化UI并开始自动同步，而不是刷新页面
            setTimeout(() => {
                UI.init();
                
                // 如果启用了自动同步，立即开始
                if (CONFIG.AUTO_SCRAPE) {
                    setTimeout(() => {
                        this.startAutoScrapeAfterSetup();
                    }, 2000); // 延迟2秒确保UI完全初始化
                }
            }, 500);
        },

        // 完成设置后的自动抓取（忽略已存在数据的检查）
        startAutoScrapeAfterSetup: function() {
            Utils.log('INFO', '完成首次设置，启动自动抓取和上传');
            
            const pageType = Utils.getPageType();
            const existingQualifications = GM_getValue('scraped_qualifications', []);
            const existingAccount = GM_getValue('scraped_account', null);

            if (pageType === 'account') {
                if (existingAccount) {
                    // 如果已有账户数据，直接自动上传
                    Utils.log('INFO', '发现已存在的VC账户数据，直接上传');
                    GM_notification('自动同步', 'VC账户数据已存在，正在自动上传...', 'info');
                    setTimeout(async () => {
                        await UI.autoUploadAccount(existingAccount);
                    }, 1000);
                } else {
                    // 没有数据，开始抓取
                    GM_notification('自动同步', '检测到VC账户页面，5秒后开始自动同步...', 'info');
                    setTimeout(async () => {
                        await UI.handleScrapeAccount();
                    }, 5000);
                }

            } else if (pageType === 'qualification') {
                if (existingQualifications.length > 0) {
                    // 如果已有资质数据，直接自动上传
                    Utils.log('INFO', `发现已存在${existingQualifications.length}条资质数据，直接上传`);
                    GM_notification('自动同步', `发现${existingQualifications.length}条资质数据，正在自动上传...`, 'info');
                    setTimeout(async () => {
                        await UI.autoUploadAfterScrape(existingQualifications);
                    }, 1000);
                } else {
                    // 没有数据，开始抓取
                    GM_notification('自动同步', '检测到资质页面，8秒后开始自动同步...', 'info');
                    setTimeout(async () => {
                        await UI.handleScrapeQualifications();
                    }, 8000);
                }

            } else {
                // 未识别页面，检查是否有任何已抓取的数据需要上传
                if (existingAccount || existingQualifications.length > 0) {
                    Utils.log('INFO', '未识别页面类型，但发现已存在数据，尝试上传');
                    GM_notification('自动同步', '发现已抓取的数据，正在自动上传...', 'info');
                    
                    setTimeout(async () => {
                        try {
                            let uploadCount = 0;
                            
                            // 上传VC账户数据
                            if (existingAccount) {
                                await UI.autoUploadAccount(existingAccount);
                                uploadCount++;
                            }
                            
                            // 上传资质数据
                            if (existingQualifications.length > 0) {
                                await UI.autoUploadAfterScrape(existingQualifications);
                                uploadCount += existingQualifications.length;
                            }
                            
                            if (uploadCount > 0) {
                                Utils.log('INFO', `自动上传完成，共处理${uploadCount}条数据`);
                            }
                            
                        } catch (error) {
                            Utils.log('ERROR', '自动上传失败', error.message);
                            GM_notification('自动上传失败: ' + error.message, 'error');
                        }
                    }, 2000);
                } else {
                    Utils.log('INFO', '未识别页面类型且无已存在数据');
                    GM_notification('提示', '未识别的京麦页面类型，请手动导航到VC账户或资质管理页面', 'info');
                }
            }
        },

        // 重置到首次设置状态
        resetToFirstSetup: function() {
            // 显示更详细的确认对话框
            const confirmMessage = `确定要重置所有设置吗？

这将清除以下内容：
• API服务器地址
• 管理员姓名设置
• 自动同步开关
• 已抓取的数据

重置后页面将自动刷新，回到首次设置状态。`;

            if (confirm(confirmMessage)) {
                try {
                    // 清除所有配置，使用兼容的方式
                    if (typeof GM_deleteValue !== 'undefined') {
                        // 如果GM_deleteValue可用，使用它
                        GM_deleteValue('API_BASE_URL');
                        GM_deleteValue('AUTO_SCRAPE');
                        GM_deleteValue('MANAGER_NAME');
                        GM_deleteValue('FIRST_SETUP_DONE');
                        GM_deleteValue('scraped_account');
                        GM_deleteValue('scraped_qualifications');
                    } else {
                        // 如果GM_deleteValue不可用，设置为默认值
                        GM_setValue('API_BASE_URL', CONFIG.API_BASE_URL);
                        GM_setValue('AUTO_SCRAPE', true);
                        GM_setValue('MANAGER_NAME', '');
                        GM_setValue('FIRST_SETUP_DONE', false);
                        GM_setValue('scraped_account', null);
                        GM_setValue('scraped_qualifications', []);
                    }

                    // 更新当前配置
                    CONFIG.AUTO_SCRAPE = true;
                    CONFIG.MANAGER_NAME = '';
                    CONFIG.FIRST_SETUP_DONE = false;

                    // 显示提示
                    GM_notification('重置完成', '所有设置已清除，页面将自动刷新进入首次设置', 'success');

                    // 延迟刷新页面
                    setTimeout(() => {
                        location.reload();
                    }, 2000);

                } catch (error) {
                    console.error('重置设置时出错:', error);
                    GM_notification('重置失败', '重置过程中出现错误: ' + error.message, 'error');
                }
            }
        },

        // 启动自动抓取
        startAutoScrape: function() {
            const pageType = Utils.getPageType();
            console.log('🔍 [页面检测] 当前页面类型:', pageType);
            
            // 检查是否已有数据，避免重复抓取
            const existingQualifications = GM_getValue('scraped_qualifications', []);
            const existingAccount = GM_getValue('scraped_account', null);

            if (pageType === 'account') {
                if (existingAccount) {
                    // 如果已有账户数据，检查是否需要重新上传
                    const syncedVcInfo = ApiClient.getSyncedVcAccountInfo();
                    const lastAccountUploadTime = GM_getValue('last_account_upload_time', '');
                    const today = new Date().toDateString();
                    
                    // 检查是否今天已经上传过VC账户
                    const isAccountUploadedToday = lastAccountUploadTime === today;
                    const hasValidSync = syncedVcInfo && syncedVcInfo.vcAccountId;
                    
                    if (isAccountUploadedToday && hasValidSync) {
                        Utils.log('INFO', 'VC账户数据已存在且今日已上传，跳过自动处理');
                        GM_notification('提示', 'VC账户数据今日已同步，无需重复处理', 'info');
                    } else {
                        // 数据存在但今日未上传，自动上传
                        const reason = !isAccountUploadedToday ? '今日尚未上传' : '同步信息缺失';
                        Utils.log('INFO', `VC账户数据已存在但${reason}，开始自动上传`);
                        GM_notification('自动同步', 'VC账户数据已存在，正在自动上传...', 'info');
                        setTimeout(async () => {
                            await UI.autoUploadAccount(existingAccount);
                        }, 3000);
                    }
                    return;
                }
                
                // 在账户页面自动抓取账户信息
                GM_notification('自动同步', '检测到VC账户页面，10秒后开始自动同步...', 'info');
                setTimeout(async () => {
                    await UI.handleScrapeAccount();
                }, 10000); // 延长到10秒，避免频繁请求

            } else if (pageType === 'qualification') {
                if (existingQualifications.length > 0) {
                    // 如果已有资质数据，检查是否需要重新上传
                    const uploadSuccessCount = GM_getValue('upload_success_count', 0);
                    const lastUploadTime = GM_getValue('last_upload_time', '');
                    const today = new Date().toDateString(); // 获取今天的日期字符串
                    
                    // 检查是否今天已经上传过，且上传数量匹配
                    const isUploadedToday = lastUploadTime === today;
                    const isFullyUploaded = uploadSuccessCount > 0 && uploadSuccessCount >= existingQualifications.length;
                    
                    if (isUploadedToday && isFullyUploaded) {
                        Utils.log('INFO', `资质数据已存在${existingQualifications.length}条且今日已上传，跳过自动处理`);
                        GM_notification('提示', `资质数据今日已同步，共${existingQualifications.length}条记录`, 'info');
                    } else {
                        // 数据存在但今日未上传或未完全上传，自动上传
                        const reason = !isUploadedToday ? '今日尚未上传' : '未完全上传';
                        Utils.log('INFO', `发现${existingQualifications.length}条资质数据${reason}，开始自动上传`);
                        GM_notification('自动同步', `发现${existingQualifications.length}条资质数据，正在自动上传...`, 'info');
                        setTimeout(async () => {
                            await UI.autoUploadAfterScrape(existingQualifications);
                        }, 3000);
                    }
                    return;
                }
                
                // 在资质页面自动抓取资质数据
                GM_notification('自动同步', '检测到资质页面，15秒后开始自动同步...', 'info');
                setTimeout(async () => {
                    await UI.handleScrapeQualifications();
                }, 15000); // 进一步延长到15秒，给页面充分加载时间
                
            } else {
                Utils.log('INFO', '未识别的页面类型，跳过自动同步');
                GM_notification('提示', '未识别的京麦页面类型', 'info');
            }
        }
    };
    
    // 启动应用
    Main.init();

    // 暴露重置方法到全局，方便控制台调用
    window.resetJingmaiScript = function() {
        Main.resetToFirstSetup();
    };

    // 暴露设置方法到全局，方便控制台调用
    window.showJingmaiSettings = function() {
        UI.showSettingsDialog();
    };

    // 暴露调试方法到全局
    window.checkJingmaiData = function() {
        try {
            const accountData = GM_getValue('scraped_account', null);
            const qualifications = GM_getValue('scraped_qualifications', []);
            const syncedVcInfo = GM_getValue('vc_account_sync_result', null);
            const uploadSuccessCount = GM_getValue('upload_success_count', 0);
            const lastUploadTime = GM_getValue('last_upload_time', '');
            const lastAccountUploadTime = GM_getValue('last_account_upload_time', '');
            const today = new Date().toDateString();
            
            console.log('存储数据检查:');
            console.log('- VC账户数据:', accountData);
            console.log('- 资质数据数量:', Array.isArray(qualifications) ? qualifications.length : 0);
            console.log('- 已同步VC账户信息:', syncedVcInfo);
            console.log('- 上传成功数量:', uploadSuccessCount);
            console.log('- 最后上传时间:', lastUploadTime);
            console.log('- 最后VC账户上传时间:', lastAccountUploadTime);
            console.log('- 今天日期:', today);
            console.log('- 资质是否今日已上传:', lastUploadTime === today);
            console.log('- VC账户是否今日已上传:', lastAccountUploadTime === today);
            
            return {
                accountData,
                qualifications,
                syncedVcInfo,
                uploadSuccessCount,
                lastUploadTime,
                lastAccountUploadTime,
                today,
                isQualificationUploadedToday: lastUploadTime === today,
                isAccountUploadedToday: lastAccountUploadTime === today
            };
        } catch (error) {
            console.error('检查数据失败:', error);
            return null;
        }
    };

    window.checkJingmaiIntercept = function() {
        try {
            if (DataScraper && typeof DataScraper.checkInterceptStatus === 'function') {
                return DataScraper.checkInterceptStatus();
            } else {
                console.error('DataScraper模块未初始化');
                return null;
            }
        } catch (error) {
            console.error('检查拦截状态失败:', error);
            return null;
        }
    };

    window.clearJingmaiData = function() {
        try {
            GM_setValue('scraped_account', null);
            GM_setValue('scraped_qualifications', []);
            GM_setValue('vc_account_sync_result', null);
            GM_setValue('upload_success_count', 0);
            GM_setValue('last_upload_time', '');
            GM_setValue('last_account_upload_time', '');
            
            console.log('v1.0.1: 已清理所有抓取数据和上传记录，可以重新开始抓取');
            
            // 更新UI统计
            if (typeof UI !== 'undefined' && UI.updateStatistics) {
                UI.updateStatistics();
            }
            
            return '数据清理完成 v1.0.1';
        } catch (error) {
            console.error('清理数据失败:', error);
            return '清理失败: ' + error.message;
        }
    };

    window.reinitJingmaiIntercept = function() {
        try {
            if (DataScraper && typeof DataScraper.reinitializeIntercept === 'function') {
                console.log('🔄 手动重新初始化拦截机制');
                DataScraper.reinitializeIntercept();
                return '拦截机制已重新初始化';
            } else {
                console.error('DataScraper模块未初始化');
                return '初始化失败';
            }
        } catch (error) {
            console.error('重新初始化拦截失败:', error);
            return '初始化失败: ' + error.message;
        }
    };

    // 暴露强制重置方法（用于紧急情况）
    window.forceResetJingmaiScript = function() {
        try {
            // 强制重置所有配置为默认值
            GM_setValue('API_BASE_URL', CONFIG.API_BASE_URL);
            GM_setValue('AUTO_SCRAPE', true);
            GM_setValue('MANAGER_NAME', '');
            GM_setValue('FIRST_SETUP_DONE', false);
            GM_setValue('scraped_account', null);
            GM_setValue('scraped_qualifications', []);

            console.log('强制重置完成，即将刷新页面...');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } catch (error) {
            console.error('强制重置失败:', error);
        }
    };

    // 设备限制检测和解决方案工具
    window.deviceBlockSolution = function() {
        console.group('🚫 [设备限制] 解决方案指南');
        
        // 检测当前限制状态
        console.log('📊 当前状态检测:');
        console.log('- 设备指纹:', navigator.userAgent.substring(0, 50) + '...');
        console.log('- 屏幕分辨率:', `${screen.width}x${screen.height}`);
        console.log('- 时区:', Intl.DateTimeFormat().resolvedOptions().timeZone);
        console.log('- 语言:', navigator.language);
        console.log('- 硬件并发:', navigator.hardwareConcurrency);
        
        console.log('\n🔧 解决方案（按优先级排序）:');
        console.log('1. 🌐 网络切换方案:');
        console.log('   - 使用手机热点切换网络');
        console.log('   - 使用VPN更改IP地址');
        console.log('   - 切换到其他网络环境（如公司网络）');
        
        console.log('\n2. 🖥️ 设备环境方案:');
        console.log('   - 使用无痕/隐私模式浏览器');
        console.log('   - 更换不同的浏览器（Edge/Firefox/Chrome）');
        console.log('   - 使用虚拟机或其他设备');
        
        console.log('\n3. ⏰ 时间策略方案:');
        console.log('   - 等待至少2-4小时后重试');
        console.log('   - 在非工作时间（晚上/周末）重试');
        console.log('   - 间隔24小时后重新尝试');
        
        console.log('\n4. 🔄 浏览器重置方案:');
        console.log('   - 执行 clearBrowserFingerprint() 清理浏览器指纹');
        console.log('   - 清除所有京麦相关的存储数据');
        console.log('   - 重置浏览器配置文件');
        
        console.groupEnd();
        
        return {
            solution1: '网络切换（最推荐）',
            solution2: '设备环境更换',
            solution3: '时间延迟策略',
            solution4: '浏览器指纹重置',
            quickTest: '运行 testNetworkConnection() 测试网络状态'
        };
    };

    // 浏览器指纹清理工具
    window.clearBrowserFingerprint = function() {
        try {
            console.log('🧹 开始清理浏览器指纹...');
            
            // 清除所有本地存储
            localStorage.clear();
            sessionStorage.clear();
            
            // 清除所有京麦相关cookies
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            // 清除GM脚本数据（非配置数据）
            GM_setValue('cached_real_response', null);
            GM_setValue('scrape_logs', []);
            
            console.log('✅ 浏览器指纹清理完成');
            console.log('💡 建议：重启浏览器或使用无痕模式访问京麦');
            
            return '指纹清理完成，请重启浏览器';
        } catch (error) {
            console.error('❌ 指纹清理失败:', error);
            return '清理失败: ' + error.message;
        }
    };

    // 网络连接测试工具
    window.testNetworkConnection = function() {
        console.log('🌐 测试网络连接状态...');
        
        // 获取当前IP信息
        fetch('https://ipapi.co/json/')
            .then(response => response.json())
            .then(data => {
                console.log('📍 当前网络信息:');
                console.log('- IP地址:', data.ip);
                console.log('- 位置:', `${data.city}, ${data.country_name}`);
                console.log('- ISP:', data.org);
                console.log('- 时区:', data.timezone);
            })
            .catch(error => {
                console.log('⚠️ 无法获取IP信息:', error.message);
            });
            
        // 测试京麦连接
        fetch('https://shop.jd.com', {method: 'HEAD'})
            .then(response => {
                console.log('🔗 京麦连接状态:', response.status === 200 ? '正常' : '异常');
            })
            .catch(error => {
                console.log('❌ 京麦连接失败:', error.message);
            });
            
        return '网络测试已启动，请查看控制台结果';
    };

    // 智能重试工具（超保守模式）
    window.smartRetryMode = function() {
        console.log('🎯 启动智能重试模式（超保守）');
        
        const retryConfig = {
            baseDelay: 3600000,      // 1小时基础延迟
            maxDelay: 14400000,      // 最大4小时延迟  
            maxRetries: 3,           // 最多3次重试
            currentRetry: 0
        };
        
        const executeRetry = async (retryCount) => {
            const delay = Math.min(
                retryConfig.baseDelay * Math.pow(2, retryCount) + Math.random() * 1800000,
                retryConfig.maxDelay
            );
            
            console.log(`⏳ 第${retryCount + 1}次重试将在 ${Math.round(delay/60000)} 分钟后执行`);
            
            // 设置定时器
            setTimeout(async () => {
                try {
                    console.log(`🚀 开始第${retryCount + 1}次重试`);
                    
                    // 先测试网络环境
                    await testNetworkConnection();
                    
                    // 清理环境
                    clearBrowserFingerprint();
                    
                    console.log('✅ 环境准备完成，可以手动尝试操作页面');
                    GM_notification('智能重试', `第${retryCount + 1}次重试已准备完成，请手动操作`, 'info');
                    
                } catch (error) {
                    console.error(`❌ 第${retryCount + 1}次重试失败:`, error);
                    
                    if (retryCount < retryConfig.maxRetries - 1) {
                        executeRetry(retryCount + 1);
                    } else {
                        console.log('❌ 所有重试已用完，建议更换设备或网络环境');
                        GM_notification('重试完成', '建议更换设备或网络环境', 'error');
                    }
                }
            }, delay);
        };
        
        executeRetry(0);
        return `智能重试模式已启动，共${retryConfig.maxRetries}次重试机会`;
    };

    // 暴露测试拦截功能的方法
    window.testJingmaiIntercept = function() {
        console.log('🧪 [测试拦截] 尝试发送测试请求到京麦API');
        
        const testRequestBody = {
            queryParam: {
                brandCode: null,
                purchaserCode: null,
                brandType: null,
                auditState: null,
                pageNum: 1,
                pageSize: 50,
                vendorCode: null
            },
            callerParam: {
                verticalCode: "cn_retail_vc",
                buid: 301,
                tenantId: 1024,
                site: "301",
                terminal: "PC"
            },
            accessContext: {
                source: "web",
                timestamp: Date.now()
            }
        };
        
        // 使用GM_xmlhttpRequest发送测试请求，观察是否能被拦截
        GM_xmlhttpRequest({
            method: 'POST',
            url: CONFIG.JINGMAI_APIS.QUALIFICATION_LIST,
            headers: {
                'Content-Type': 'application/json;charset=UTF-8',
                'User-Agent': navigator.userAgent,
                'Referer': window.location.href
            },
            data: JSON.stringify(testRequestBody),
            timeout: 10000,
            onload: function(response) {
                console.log('🧪 [测试拦截] 测试请求响应:', response);
                console.log('📝 检查控制台是否有拦截日志');
            },
            onerror: function(error) {
                console.log('❌ [测试拦截] 测试请求失败:', error);
            }
        });
        
        return '测试请求已发送，请查看控制台日志';
    };

    // 暴露停止拦截的方法
    window.stopJingmaiIntercept = function() {
        if (DataScraper) {
            DataScraper.interceptRetryCount = DataScraper.maxInterceptRetries; // 停止自动重试
            console.log('🛑 [拦截控制] 已停止自动重新初始化拦截');
            return '拦截自动重试已停止';
        }
        return '拦截器未找到';
    };

    // 更新脚本状态
    window.jingmaiScriptStatus = 'loaded';
    console.log('✅ [京麦脚本] 主脚本执行完成，已启用全局拦截器 v1.0.1 (已移除remark字段)');
    console.log('🧪 [调试方法] 现在可以使用以下命令：');
    console.log('  - checkJingmaiData() - 查看存储数据');
    console.log('  - checkJingmaiIntercept() - 检查拦截状态'); 
    console.log('  - clearJingmaiData() - 清理数据重新开始');
    console.log('  - reinitJingmaiIntercept() - 重新初始化拦截');
    console.log('  - testJingmaiIntercept() - 发送测试请求验证拦截');
    console.log('  - stopJingmaiIntercept() - 停止自动重新初始化');
    console.log('🚫 [设备限制解决方案] 以下是处理设备被限制的工具：');
    console.log('  - deviceBlockSolution() - 查看完整解决方案指南');
    console.log('  - clearBrowserFingerprint() - 清理浏览器指纹');
    console.log('  - testNetworkConnection() - 测试网络环境');
    console.log('  - smartRetryMode() - 启动智能重试模式（超保守）');

})();

console.log('🎉 [京麦脚本] 脚本加载完成！');
console.log('🧪 [京麦脚本] 请运行 testJingmaiScriptLoaded() 确认脚本状态');
console.log('🧪 [调试命令] 现在可以使用以下调试命令：');
console.log('  - checkJingmaiData() - 查看存储数据');
console.log('  - checkJingmaiIntercept() - 检查拦截状态');
console.log('  - clearJingmaiData() - 清理数据重新开始');
console.log('  - reinitJingmaiIntercept() - 重新初始化拦截');
console.log('🚫 [设备限制] 如果遇到请求被限制的问题：');
console.log('  - deviceBlockSolution() - 查看解决方案（重要！）');
console.log('  - clearBrowserFingerprint() - 清理浏览器指纹');
console.log('  - testNetworkConnection() - 测试网络状态');
console.log('  - smartRetryMode() - 启动超保守重试模式');